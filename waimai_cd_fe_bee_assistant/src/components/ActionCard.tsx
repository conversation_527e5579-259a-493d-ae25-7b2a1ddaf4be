import { openPage } from '@mfe/bee-foundation-utils';
import { TouchableOpacity, Text, View, StyleSheet } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Toast } from '@roo/roo-rn';
import React from 'react';

import AICallModal from './AICallModal/AICallModal';
import { useAICallModal } from '../hooks/useAICallModal';
import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../types';
import { ActionCardMessage, ActionCardButton } from '../types/message';

interface Props {
    data: ActionCardMessage['insert']['actionCard'];
    onEndTyping?: () => void;
}

const styles = StyleSheet.create({
    gradientContainer: {
        borderRadius: 12,
        marginVertical: 8,
    },
    container: {
        borderWidth: 1,
        borderColor: '#B8C1FF',
        borderRadius: 11,
        paddingHorizontal: 15,
        paddingVertical: 11,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    contentContainer: {
        flex: 1,
        marginRight: 12,
    },
    title: {
        fontSize: 16,
        fontWeight: '700',
        color: '#6047FA',
        marginBottom: 4,
    },
    subTitle: {
        fontSize: 12,
        color: '#666666',
        lineHeight: 16,
    },
    button: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    primaryButton: {
        backgroundColor: '#FFD100',
    },
    normalButton: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    primaryButtonText: {
        color: '#333333',
    },
    normalButtonText: {
        color: '#333333',
    },
    // 多按钮样式
    buttonContainer: {
        flexDirection: 'row',
    },
    multiButton: {
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 32,
    },
    multiButtonText: {
        fontSize: 12,
        fontWeight: '500',
    },
});

const ActionCard = ({ data }: Props) => {
    const { button, buttonList, title, subTitle } = data;
    const { send } = useSendMessage();
    const { visible, openModal, closeModal, submitTask, initialParams } =
        useAICallModal();

    const handleButtonPress = async (buttonData: ActionCardButton) => {
        try {
            // 优先处理action
            if (buttonData.action === 'submitQuestion' && buttonData.question) {
                send(
                    buttonData.question,
                    EntryPointType.USER,
                    EntryPoint.action_card,
                );
                return;
            }

            // 处理openAICallModal action
            if (buttonData.action === 'openAICallModal') {
                openModal(buttonData.AICallParams || {});
                return;
            }

            // 处理URL跳转
            if (buttonData.url) {
                openPage(buttonData.url);
                return;
            }

            // 如果没有action和url，显示提示
            Toast.open('暂无可执行的操作');
        } catch (error) {
            console.error('ActionCard button press error:', error);
            Toast.open('操作失败，请重试');
        }
    };

    const getButtonStyle = (
        buttonData: ActionCardButton,
        isMultiple = false,
    ) => {
        const baseStyle = isMultiple ? [styles.multiButton] : [styles.button];

        // 优先使用自定义颜色
        if (buttonData.color) {
            return [...baseStyle, { backgroundColor: buttonData.color }];
        }

        // 使用type决定样式
        if (buttonData.type === 'primary') {
            return [...baseStyle, styles.primaryButton];
        }

        return [...baseStyle, styles.normalButton];
    };

    const getButtonTextStyle = (
        buttonData: ActionCardButton,
        isMultiple = false,
    ) => {
        const baseStyle = isMultiple
            ? [styles.multiButtonText]
            : [styles.buttonText];

        // 如果有自定义颜色，使用对比色文字
        if (buttonData.color) {
            // 简单的对比度判断，实际项目中可能需要更复杂的算法
            const isLightColor =
                buttonData.color.toLowerCase().includes('ff') ||
                buttonData.color.toLowerCase().includes('yellow') ||
                buttonData.color.toLowerCase().includes('white');
            return [
                ...baseStyle,
                { color: isLightColor ? '#333333' : '#ffffff' },
            ];
        }

        if (buttonData.type === 'primary') {
            return [...baseStyle, styles.primaryButtonText];
        }

        return [...baseStyle, styles.normalButtonText];
    };

    // 获取要显示的按钮列表，优先使用buttonList
    const getButtons = (): ActionCardButton[] => {
        if (buttonList && buttonList.length > 0) {
            // 最多显示3个按钮
            return buttonList.slice(0, 3);
        }
        if (button) {
            return [button];
        }
        return [];
    };

    const buttons = getButtons();
    const isMultipleButtons = buttons.length > 1;

    return (
        <LinearGradient
            colors={['rgba(184,193,255, 0.28)', 'rgba(196, 229, 255, 0)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientContainer}
        >
            <View style={styles.container}>
                <View style={styles.contentContainer}>
                    <Text style={styles.title} numberOfLines={1}>
                        {title}
                    </Text>
                    <Text style={styles.subTitle} numberOfLines={2}>
                        {subTitle}
                    </Text>
                </View>

                {isMultipleButtons ? (
                    <View style={styles.buttonContainer}>
                        {buttons.map((btn, index) => (
                            <TouchableOpacity
                                key={index}
                                style={[
                                    getButtonStyle(btn, true),
                                    { marginRight: 4 },
                                ]}
                                onPress={() => handleButtonPress(btn)}
                                activeOpacity={0.8}
                            >
                                <Text
                                    style={getButtonTextStyle(btn, true)}
                                    numberOfLines={1}
                                >
                                    {btn.text}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                ) : buttons.length === 1 ? (
                    <TouchableOpacity
                        style={getButtonStyle(buttons[0])}
                        onPress={() => handleButtonPress(buttons[0])}
                        activeOpacity={0.8}
                    >
                        <Text
                            style={getButtonTextStyle(buttons[0])}
                            numberOfLines={1}
                        >
                            {buttons[0].text}
                        </Text>
                    </TouchableOpacity>
                ) : null}
            </View>

            <AICallModal
                visible={visible}
                onClose={closeModal}
                onSubmit={submitTask}
                initialParams={initialParams}
            />
        </LinearGradient>
    );
};

export default ActionCard;
