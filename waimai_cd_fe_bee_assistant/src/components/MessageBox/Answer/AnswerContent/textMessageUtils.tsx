import { Text, View, Image } from '@mrn/react-native';
import React from 'react';

import { link, pic, renderMarkdownEle } from './Markdown';
import Selector, { SelectorItemContent } from './RejectSelector';
import SelectionContent from './SelectionMessage/SelectionContent';
import WebviewMessage from './WebviewMessage/WebviewMessage';
import TWS from '../../../../TWS';
import { MessageStatus } from '../../../../types';
import { Message } from '../../../../types';
import { Message as MessageType } from '../../../../types/message';
import getKeyFromArray from '../../../../utils/getKeyFromArray';
import ActionCard from '../../../ActionCard';
import AICallRecord from '../../../AICallRecord/AICallRecord';
import Buttons from '../../../Buttons';
import { FormCard } from '../../../Chat/MessageComponent/FormCard';
import { NewPoiWeightingCard } from '../../../Chat/MessageComponent/NewPoiWeightingCard';
import ImageGallery from '../../../ImageGallery/ImageGallery';
import LinkOrNormalText from '../../../LinkOrNormalText';
import PieChart from '../../../PieChart/PieChart';
import RejectCard from '../../../RejectCard';
import Table from '../../../Table';

import CollapsibleText from '@/components/Chat/MessageComponent/CollapsibleText';
import Descriptions from '@/components/Chat/MessageComponent/Descriptions';
import ReferenceDoc from '@/components/Chat/MessageComponent/ReferenceDoc';
import Title from '@/components/Chat/MessageComponent/Title';
import RNImage from '@/components/RNImage';

// 服务TextMessage组件，提供渲染相关的几个纯函数

const styles = {
    link: {
        color: '#FF6A00',
    },
};

// markdown数据渲染完成后需要识别图片链接，该方法识别出图片链接并渲染，因会re-render，会导致UI闪烁，所以只在请求完成后才做该操作
export const castMarkdown = (children, msgId, history, listRef) => {
    const res = [];
    children.forEach((v) => {
        if (!v) {
            return;
        }
        const lastEle = res[res.length - 1];
        if (v.key.startsWith('markdown')) {
            if (Array.isArray(lastEle)) {
                lastEle.push(v);
            } else {
                res.push([v]);
            }
            return;
        }
        res.push(v);
    });
    const splitPattern = /(!\[.*?]\(.*?\))|(\[.*?]\(.*?\))/;
    return res
        .map((v) => {
            if (!Array.isArray(v)) {
                return v;
            }

            const str = v.map((ele) => ele.props.children).join('');
            if (pic.test(str) || link.test(str)) {
                const parts = str.split(splitPattern);
                const res = parts
                    .filter((v) => v && !/^\s*$/.test(v))
                    .filter(Boolean)
                    .map((p, index) => {
                        if (pic.test(p)) {
                            const { url } = pic.text2Obj(p);
                            return {
                                type: 'img',
                                url,
                                index,
                            };
                        }
                        if (link.test(p)) {
                            const { text, url } = link.text2Obj(p);
                            return {
                                type: 'link',
                                text,
                                url,
                                index,
                            };
                        }
                        return {
                            type: 'text',
                            text: p,
                            index,
                        };
                    });
                const finalRes = [];
                res.forEach((v) => {
                    const lastEle = finalRes[finalRes.length - 1];
                    if (v.type === 'img') {
                        if (lastEle?.type === 'imgList') {
                            lastEle.url.push(v.url);
                            return;
                        }
                        finalRes.push({
                            type: 'imgList',
                            url: [v.url],
                            index: v.index,
                        });
                        return;
                    }
                    finalRes.push(v);
                });
                return finalRes.map((v) => {
                    return renderMarkdownEle(
                        v,
                        false,
                        () => {},
                        20,
                        msgId,
                        history,
                        listRef,
                    );
                });
            }
            return v;
        })
        .reduce((pre: JSX.Element[], cur) => {
            if (Array.isArray(cur)) {
                return [...pre, ...cur];
            }
            return [...pre, cur];
        }, []) as JSX.Element[];
};

// 用来给相邻的Text组件添加Text父组件，1：使它们能够共同被选择，而不是分块选中；2：使它们能够共同渲染，而不是换行再渲染
export const cast = (children: any[], status, msgId, history, listRef) => {
    const res = [];
    children = children.filter(Boolean);
    // 输出完后尝试解析大模型文案中的图片链接
    const markdownRes =
        status === MessageStatus.DONE
            ? castMarkdown(children, msgId, history, listRef)
            : children;
    markdownRes.forEach((v) => {
        if (!v) {
            return;
        }
        const lastEle = res[res.length - 1];
        if (
            String(v.key).startsWith('text') ||
            String(v.key).startsWith('inlineImage') ||
            (String(v.key).startsWith('markdown') &&
                !String(v.key).endsWith('hasPic'))
        ) {
            if (Array.isArray(lastEle)) {
                lastEle.push(v);
            } else {
                res.push([v]);
            }
            return;
        }
        res.push(v);
    });
    return res.map((v, i) => {
        if (Array.isArray(v)) {
            return (
                <Text
                    selectable
                    style={{ lineHeight: 21, fontSize: 14 }}
                    key={i}
                >
                    {v}
                </Text>
            );
        }
        return v;
    });
};

// 根据数据渲染指定组件
export const renderEle = (data: Message) => (v: MessageType, index) => {
    if (!v) {
        return null;
    }
    switch (v.type) {
        case 'text':
            return (
                <LinkOrNormalText
                    key={getKeyFromArray(['text', v.insert, String(+index)])}
                    msgId={data.msgId}
                    history={data.history}
                >
                    {v.insert}
                </LinkOrNormalText>
            );
        case 'link':
            return (
                <LinkOrNormalText
                    key={getKeyFromArray(['text', v.insert, String(+index)])}
                    url={v.attributes.link}
                    style={[styles.link]}
                    msgId={data.msgId}
                    history={data.history}
                >
                    {v.insert}
                </LinkOrNormalText>
            );
        case 'styledText':
            return (
                <LinkOrNormalText
                    style={[
                        v.attributes.bold && { fontWeight: 'bold' },
                        v.attributes.color && {
                            color: v.attributes.color,
                        },
                    ]}
                    key={getKeyFromArray(['text', v.insert, String(index)])}
                    msgId={data.msgId}
                    history={data.history}
                >
                    {v.insert}
                </LinkOrNormalText>
            );
        case 'media':
            return (
                <ImageGallery
                    key={getKeyFromArray([v.type, String(index)])}
                    style={{ marginTop: 8, marginBottom: 0 }}
                    images={v.insert.media}
                />
            );
        case 'buttons':
            return (
                <Buttons
                    key={getKeyFromArray([v.type, String(index)])}
                    data={v.insert.buttons}
                />
            );
        case 'options':
            return (
                <SelectionContent
                    key={getKeyFromArray([v.type, String(index)])}
                    data={{
                        ...data,
                        selectionItems: v.insert.options as any,
                    }}
                />
            );
        case 'suffixOptions':
        case 'config':
        case 'addition':
            return null;
        case 'markdown':
            return null;
        case 'hideSpan':
            return null;
        case 'actionCard':
            return (
                <ActionCard
                    key={getKeyFromArray([v.type, String(index)])}
                    data={v.insert.actionCard}
                />
            );
        case 'rejectCard':
            return (
                <RejectCard
                    key={getKeyFromArray([v.type, String(index)])}
                    data={v.insert.rejectCard}
                />
            );
        case 'cardWithAvatar':
            return (
                <View
                    style={[TWS.row()]}
                    key={getKeyFromArray([
                        v.type,
                        v.insert.cardWithAvatar.title,
                    ])}
                >
                    <View
                        style={[
                            TWS.square(36),
                            {
                                borderRadius: 8,
                                overflow: 'hidden',
                                marginRight: 8,
                            },
                        ]}
                    >
                        <Image
                            source={{ uri: v.insert.cardWithAvatar.avatar }}
                            style={[TWS.square(36), { borderRadius: 8 }]}
                        />
                    </View>
                    <View style={{ flex: 1 }}>
                        <Text
                            style={{ fontSize: 16, color: '#222' }}
                            ellipsizeMode={'middle'}
                            numberOfLines={1}
                        >
                            {v.insert.cardWithAvatar.title}
                        </Text>
                        <View style={{ flexWrap: 'wrap' }}>
                            {v.insert.cardWithAvatar.content.map((c) => {
                                return (
                                    <Text
                                        style={{
                                            fontSize: 14,
                                            color: '#666',
                                        }}
                                    >
                                        {c.label}:{c.value}
                                    </Text>
                                );
                            })}
                        </View>
                    </View>
                </View>
            );
        case 'selectorItem':
            return (
                <SelectorItemContent
                    data={v.insert.selectorItem}
                    key={v.type}
                />
            );
        case 'selector':
            return <Selector data={v} key={v.type} />;
        case 'webview':
            return <WebviewMessage data={v} key={`${v.type}-${index}`} />;
        case 'table':
            return (
                <Table
                    {...v.insert.table}
                    key={`${v.type}-${index}`}
                    style={index !== 0 ? { marginTop: 10 } : undefined}
                />
            );
        case 'newPoiWeightingCard':
            return (
                <NewPoiWeightingCard
                    {...v.insert.newPoiWeightingCard}
                    key={`${v.type}-${index}`}
                />
            );
        case 'form':
            return (
                <FormCard
                    history={data.history}
                    {...v.insert.form}
                    key={`${v.type}-${index}`}
                />
            );

        case 'collapsibleText':
            return (
                <CollapsibleText
                    {...v.insert.collapsibleText}
                    msgId={data.msgId}
                    history={data.history}
                    key={`${v.type}-${index}`}
                />
            );
        case 'descriptions':
            return (
                <Descriptions
                    {...v.insert.descriptions}
                    key={`${v.type}-${index}`}
                />
            );

        case 'title':
            return <Title {...v.insert.title} key={`${v.type}-${index}`} />;
        case 'referenceDoc':
            return (
                <ReferenceDoc
                    {...v.insert.referenceDoc}
                    key={`${v.type}-${index}`}
                />
            );
        case 'inlineImage':
            return (
                <RNImage
                    key={`${v.type}-${index}`}
                    source={{ uri: v.insert.inlineImage }}
                    style={{ height: 14 }}
                />
            );
        case 'pieChart':
            return (
                <PieChart
                    key={getKeyFromArray([v.type, String(index)])}
                    data={v.insert.pieChart.data}
                    title={v.insert.pieChart.title}
                    size={v.insert.pieChart.size || 200}
                    showLegend={v.insert.pieChart.showLegend ?? true}
                    showPercent={v.insert.pieChart.showPercent ?? true}
                />
            );
        case 'aiCallRecord':
            return (
                <AICallRecord
                    key={getKeyFromArray([v.type, String(index)])}
                    data={v.insert.aiCallRecord}
                />
            );
        default:
            return <Text key={v.type}>该类型消息暂不支持，请升级后重试！</Text>;
    }
};
