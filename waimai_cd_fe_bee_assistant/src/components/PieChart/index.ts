export { default } from './PieChart';
export { default as <PERSON><PERSON><PERSON> } from './PieChart';

// 导出类型定义
export interface PieChartData {
    label: string;
    value: number;
    color?: string;
}

export interface PieChartProps {
    data: PieChartData[];
    size?: number;
    showLegend?: boolean;
    showPercent?: boolean;
    title?: string;
    innerRadius?: number;
    showTooltip?: boolean;
    animationDuration?: number;
}
