import { View, StyleSheet, Text } from '@mrn/react-native';
import { Svg<PERSON><PERSON>, SVGRenderer } from '@wuba/react-native-echarts';
import { PieChart as EChartsPieChart } from 'echarts/charts';
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import type { EChartsCoreOption } from 'echarts/core';
import React, { useRef, useEffect, useMemo } from 'react';

// ECharts 类型导入

// 组合 ECharts Option 类型
type ECOption = EChartsCoreOption;

// 注册 ECharts 组件
echarts.use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    SVGRenderer,
    EChartsPieChart,
]);

interface PieChartData {
    label: string;
    value: number;
    color?: string;
}

interface PieChartProps {
    data: PieChartData[];
    size?: number;
    showLegend?: boolean;
    showPercent?: boolean;
    title?: string;
    innerRadius?: number; // 内圆半径，默认40为环形图，设为0则为标准饼图
    showTooltip?: boolean;
    animationDuration?: number;
}

// 美团品牌色系
const MEITUAN_COLORS = [
    '#FFC300', // 美团黄
    '#FF6B35', // 美团橙
    '#32CD32', // 成功绿
    '#1890FF', // 蓝色
    '#722ED1', // 紫色
    '#FA8C16', // 橙色
    '#52C41A', // 绿色
    '#13C2C2', // 青色
];

const PieChart: React.FC<PieChartProps> = ({
    data,
    showLegend = true,
    showPercent = true,
    title,
    innerRadius = 30,
    showTooltip = true,
    animationDuration = 1000,
}) => {
    const chartRef = useRef<any>(null);

    // 准备图表数据
    const chartData = useMemo(() => {
        return data.map((item, index) => ({
            name: item.label,
            value: item.value,
            itemStyle: {
                color:
                    item.color || MEITUAN_COLORS[index % MEITUAN_COLORS.length],
            },
        }));
    }, [data]);

    // 计算总值
    const total = useMemo(() => {
        return data.reduce((sum, item) => sum + item.value, 0);
    }, [data]);

    // ECharts 配置
    const option: ECOption = useMemo(() => {
        const radius = [innerRadius, '60%'];

        return {
            tooltip: showTooltip
                ? {
                      trigger: 'item',
                      formatter: function (params: any) {
                          const percent = params.percent;
                          return `${params.name}: ${params.value} (${percent}%)`;
                      },
                      backgroundColor: (params: any) => {
                          return params.color;
                      },
                      textStyle: {
                          color: '#333333',
                          fontSize: 12,
                      },
                      borderWidth: 1,
                      borderColor: '#e0e0e0',
                      padding: [8, 12],
                      shadowColor: 'rgba(0, 0, 0, 0.1)',
                      shadowBlur: 4,
                      shadowOffsetY: 2,
                      // 限制 tooltip 在图表容器内
                      confine: true,
                      // 防止tooltip阻挡鼠标事件
                      hideDelay: 300, // 延迟隐藏，避免快速移动时闪烁
                      enterable: false, // 禁止鼠标进入tooltip
                      // 设置 tooltip 位置偏移，避免超出边界和遮挡点击
                      position: function (
                          point: number[],
                          params: any,
                          dom: any,
                          rect: any,
                          tooltipSize: any,
                      ) {
                          // point 是鼠标位置，tooltipSize 是 tooltip 大小，rect 是图表容器大小
                          const [mouseX, mouseY] = point;
                          const { contentSize, viewSize } = tooltipSize;
                          const [tooltipWidth, tooltipHeight] = contentSize;

                          // 增加偏移距离，避免遮挡点击区域
                          const offsetX = 20;
                          const offsetY = 20;

                          let x = mouseX + offsetX; // 默认在鼠标右下方
                          let y = mouseY + offsetY;

                          // 右边界检查
                          if (x + tooltipWidth > viewSize[0]) {
                              x = mouseX - tooltipWidth - offsetX; // 移到鼠标左侧
                          }

                          // 下边界检查
                          if (y + tooltipHeight > viewSize[1]) {
                              y = mouseY - tooltipHeight - offsetY; // 移到鼠标上方
                          }

                          // 上边界检查
                          if (y < 0) {
                              y = 10;
                          }

                          // 左边界检查
                          if (x < 0) {
                              x = 10;
                          }

                          return [x, y];
                      },
                  }
                : undefined,
            legend: {
                show: false, // 关闭ECharts内置legend，使用自定义legend
            },
            series: [
                {
                    name: '数据',
                    type: 'pie',
                    radius: radius,
                    data: chartData,
                    padAngle: 2, // 扇区间隙角度（度）
                    itemStyle: {
                        borderWidth: 1,
                        borderColor: '#f5f5f5',
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                        },
                    },
                    label: {
                        show: false, // 不显示饼图上的标签，使用图例代替
                    },
                    labelLine: {
                        show: false,
                    },
                    animationDuration: animationDuration,
                    animationType: 'scale',
                    animationEasing: 'elasticOut',
                },
            ],
            // 环形图中心显示总计
            graphic: [
                {
                    type: 'text',
                    left: 'center',
                    top: showLegend ? '30%' : 'center',
                    style: {
                        text: '总计',
                        fontSize: 12,
                        fill: '#666666',
                    },
                    z: 100,
                },
                {
                    type: 'text',
                    left: 'center',
                    top: showLegend ? '30%' : 'center',
                    style: {
                        text: total.toString(),
                        fontSize: 20,
                        fontWeight: '600',
                        fill: '#333333',
                    },
                    z: 100,
                    position: [0, 15],
                },
            ],
        } as ECOption;
    }, [
        chartData,
        title,
        showTooltip,
        showLegend,
        showPercent,
        innerRadius,
        animationDuration,
        total,
        data,
    ]);

    // 初始化图表
    useEffect(() => {
        let chart: any;
        if (chartRef.current) {
            chart = echarts.init(chartRef.current, 'light', {
                renderer: 'svg',
                width: 200,
                height: 200,
            });
            chart.setOption(option);
        }
        return () => chart?.dispose();
    }, [option]);

    // 自定义Legend组件
    const renderLegend = () => {
        if (!showLegend || data.length === 0) {
            return null;
        }

        const legendRows: PieChartData[][] = [];
        for (let i = 0; i < data.length; i += 2) {
            legendRows.push(data.slice(i, i + 2));
        }

        return (
            <View style={styles.legendContainer}>
                {legendRows.map((row, rowIndex) => (
                    <View key={rowIndex} style={styles.legendRow}>
                        {row.map((item, itemIndex) => {
                            const percentage =
                                total > 0
                                    ? ((item.value / total) * 100).toFixed(1)
                                    : '0.0';
                            const color =
                                item.color ||
                                MEITUAN_COLORS[
                                    (rowIndex * 2 + itemIndex) %
                                        MEITUAN_COLORS.length
                                ];

                            return (
                                <View
                                    key={item.label}
                                    style={styles.legendItem}
                                >
                                    <View
                                        style={[
                                            styles.legendColorBox,
                                            { backgroundColor: color },
                                        ]}
                                    />
                                    <Text style={styles.legendName}>
                                        {item.label}
                                    </Text>
                                    <Text style={styles.legendValue}>
                                        {percentage}%
                                    </Text>
                                </View>
                            );
                        })}
                    </View>
                ))}
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.chartWrapper}>
                <SvgChart ref={chartRef} />
                {renderLegend()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        backgroundColor: '#f5f5f5',
        borderRadius: 12,
        margin: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        alignItems: 'center',
    },
    chartWrapper: {
        alignItems: 'center',
    },
    chartContainer: {
        // 动态尺寸在组件中设置
        overflow: 'visible', // 确保图表内容可见
    },
    legendContainer: {
        marginTop: 16,
        width: '100%',
    },
    legendRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: 8,
        width: '50%',
    },
    legendColorBox: {
        width: 12,
        height: 12,
        borderRadius: 2,
        marginRight: 6,
    },
    legendName: {
        fontSize: 12,
        color: '#999999',
    },
    legendValue: {
        fontSize: 12,
        color: '#000000',
        fontWeight: '500',
    },
});

export default PieChart;
