import { CreateTask } from '@mfe/crm-ai-bee';
import { View, Dimensions } from '@mrn/react-native';
import { SlideModal } from '@roo/roo-rn';
import React from 'react';

interface AICallModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (params: any) => void;
    initialParams?: any;
}

const { height: screenHeight } = Dimensions.get('window');

const AICallModal: React.FC<AICallModalProps> = ({
    visible,
    onClose,
    // onSubmit,
    // initialParams = {},
}) => {
    // const [taskName] = useState(initialParams.taskName || '');
    // const [callScript] = useState(initialParams.callScript || '');
    // const [targetCount] = useState(initialParams.targetCount || '100');

    // const handleSubmit = () => {
    //     if (!taskName.trim()) {
    //         Toast.open('请输入任务名称');
    //         return;
    //     }

    //     if (!callScript.trim()) {
    //         Toast.open('请输入外呼话术');
    //         return;
    //     }

    //     const params = {
    //         taskName: taskName.trim(),
    //         callScript: callScript.trim(),
    //         targetCount: parseInt(targetCount) || 100,
    //         ...initialParams,
    //     };

    //     onSubmit(params);
    //     onClose();
    // };

    const onSubmitComplete = () => {
        onClose();
    };

    return (
        <SlideModal
            visible={visible}
            modalProps={{
                maskClosable: true,
                onPressClose: onClose,
            }}
        >
            <View
                style={{
                    height: screenHeight * 0.7,
                    borderRadius: 16,
                    overflow: 'hidden',
                }}
            >
                <CreateTask onSubmitComplete={onSubmitComplete} />
            </View>
        </SlideModal>
    );
};

export default AICallModal;
