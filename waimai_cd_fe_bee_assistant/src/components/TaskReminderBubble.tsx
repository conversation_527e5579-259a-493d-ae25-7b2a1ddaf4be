import { View, StyleSheet, Text, TouchableOpacity } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';
import Svg, { Path, LinearGradient, Defs, Stop, Rect } from 'react-native-svg';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 12,
        paddingVertical: 10,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 5,
        borderRadius: 10,
        marginRight: 10,
    },
    gradientContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 10,
        overflow: 'hidden',
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        zIndex: 1,
    },
    icon: {
        width: 18,
        height: 18,
        marginRight: 4,
    },
    iconContainer: {
        width: 14,
        height: 14,
        marginRight: 4,
        borderRadius: 7,
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: '#6047FA',
        borderWidth: 1,
    },
    text: {
        fontSize: 14,
        color: '#999',
    },
    arrowContainer: {
        position: 'absolute',
        right: -8,
        top: '50%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 12,
    },
});

export interface TaskReminderBubbleProps {
    onPress?: () => void;
    /** 提醒文本内容 */
    text?: string;
}

const TaskReminderBubble = (props: TaskReminderBubbleProps) => {
    const { text = '商家诊断任务已全部完成' } = props;

    const handlePress = () => {
        props.onPress?.();
    };

    // 解析文本，提取任务类型和状态
    const parseTaskText = (taskText: string) => {
        // 匹配格式："{任务名称}任务已完成" 或直接显示
        const match = taskText.match(/^(.+?)任务已(.+)$/);
        if (match) {
            return {
                taskType: match[1],
                status: `任务已${match[2]}`,
            };
        }
        // 兼容原有格式
        if (taskText.includes('商家诊断')) {
            return {
                taskType: '商家诊断',
                status: '任务已全部完成',
            };
        }
        // 默认格式
        return {
            taskType: taskText.replace(/任务已.+$/, '') || '任务',
            status: taskText.includes('完成') ? '任务已完成' : '有新提醒',
        };
    };

    const { taskType, status } = parseTaskText(text);

    return (
        <TouchableOpacity
            onPress={handlePress}
            activeOpacity={0.8}
            style={[
                styles.container,
                {
                    width: 220,
                    height: 40,
                    position: 'relative',
                },
            ]}
        >
            {/* 渐变背景 */}
            <View style={styles.gradientContainer}>
                <Svg
                    width="100%"
                    height="100%"
                    style={{ position: 'absolute' }}
                >
                    <Defs>
                        <LinearGradient
                            id="bubbleGradient"
                            x1="0%"
                            y1="0%"
                            x2="100%"
                            y2="0%"
                        >
                            <Stop offset="0%" stopColor="#E8F0FE" />
                            <Stop offset="100%" stopColor="#F1FDFF" />
                        </LinearGradient>
                    </Defs>
                    <Rect
                        width="100%"
                        height="100%"
                        fill="url(#bubbleGradient)"
                    />
                </Svg>
            </View>

            {/* 内容 */}
            <View style={styles.contentContainer}>
                <View style={styles.iconContainer}>
                    <Icon type="check" size={8} tintColor="#6047FA" />
                </View>

                <Text
                    style={[
                        styles.text,
                        { color: '#222', marginRight: 4, fontWeight: '500' },
                    ]}
                >
                    {taskType}
                </Text>
                <Text style={styles.text}>{status}</Text>
                <Icon type="right" size={12} tintColor="#444" />
            </View>
            {/* SVG 气泡箭头 - 指向右侧 */}
            <View style={styles.arrowContainer}>
                <Svg width={8} height={16} viewBox="0 0 8 16">
                    {/* 箭头主体 */}
                    <Path d="M0 0 Q6 4 8 8 Q6 12 0 16 Z" fill="#F1FDFF" />
                </Svg>
            </View>
        </TouchableOpacity>
    );
};

export default TaskReminderBubble;
