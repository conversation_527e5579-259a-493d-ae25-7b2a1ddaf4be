import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    SectionList,
    Dimensions,
    Modal,
    Animated,
    Platform,
    ScrollView,
} from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import dayjs from 'dayjs';
import React, { useState, useEffect, useMemo, useRef } from 'react';

import AICallItem from './AICallItem';
import LeftArrowIcon from './LeftArrowIcon'; // 导入图标
import TaskListItem from './TaskListItem';
import { TaskItem, TaskJob, getTaskList } from '../../api/taskApi';

// 扩展TaskItem类型，包含createTime
interface TaskItemWithTime extends TaskItem {
    createTime: string;
    taskType?: 'PoiDiagnosis' | 'AiCall';
    agentName?: string;
    jobId?: string;
    poiNum?: number;
}

interface TaskListDrawerProps {
    /** 是否显示抽屉 */
    visible: boolean;
    /** 关闭抽屉回调 */
    onClose: () => void;
    /** 发送消息回调 */
    onSendMessage: (content: string) => void;
}

// 任务类型枚举和筛选选项
const filterOptions = [
    { key: 'all', label: '全部' },
    { key: 'PoiDiagnosis', label: '商家诊断' },
    { key: 'AiCall', label: '外呼任务' },
];

// 格式化日期显示
const formatDateDisplay = (dateStr: string) => {
    const targetDate = dayjs(dateStr);
    const today = dayjs();
    const yesterday = today.subtract(1, 'day');

    if (targetDate.isSame(today, 'day')) {
        return '今日';
    } else if (targetDate.isSame(yesterday, 'day')) {
        return '昨日';
    } else {
        return targetDate.format('M月D日');
    }
};

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({
    visible,
    onClose,
    onSendMessage,
}) => {
    const [taskData, setTaskData] = useState<TaskJob[]>([]);
    const [selectedFilter, setSelectedFilter] = useState<string>('all');
    const [loading, setLoading] = useState(false);
    const { top } = useSafeAreaInsets();

    // 动画值
    const translateX = useRef(new Animated.Value(screenWidth)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    // 获取任务列表数据
    const fetchTaskList = async () => {
        try {
            setLoading(true);
            const data = await getTaskList();
            if (data?.data?.jobList) {
                setTaskData(data.data.jobList);
            } else {
                setTaskData([]);
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
            Toast.open('获取任务列表失败');
            setTaskData([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchTaskList();
        }
    }, [visible]);

    // 动画控制
    useEffect(() => {
        if (visible) {
            // 打开抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        } else {
            // 关闭抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: screenWidth,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0,
                    duration: 250,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [visible, translateX, opacity]);

    // 处理查看结果
    const handleViewResult = (item: TaskItemWithTime) => {
        // 查找对应的任务
        const job = filteredTaskData.find((job) => {
            if (job.type === 'PoiDiagnosis') {
                // 商家诊断任务：通过itemList中的poiId匹配
                return job.itemList?.some(
                    (subItem) => subItem.poiId === item.poiId,
                );
            } else if (job.type === 'AiCall') {
                // 外呼任务：通过jobId匹配
                return job.jobId === item.jobId;
            }
            return false;
        });

        if (!job) {
            return;
        }

        if (job.type === 'AiCall') {
            // 外呼任务处理：使用外层的operationType和content
            if (job.operationType === '1' && job.content) {
                // URL跳转
                console.log('跳转到URL:', job.content);
                // 这里可以调用打开URL的方法
                // Linking.openURL(job.content);
            } else if (job.operationType === '2' && job.content) {
                // 继续提问
                onSendMessage(job.content);
                onClose();
            }
        } else if (job.type === 'PoiDiagnosis') {
            // 商家诊断任务处理：使用itemList中的数据
            if (
                !['success', 'completed', 'fail', 'failed'].includes(
                    item.status,
                )
            ) {
                return;
            }
            onSendMessage(item.content);
            onClose();
        }
    };

    // 处理关闭抽屉
    const handleClose = () => {
        Animated.parallel([
            Animated.timing(translateX, {
                toValue: screenWidth,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(opacity, {
                toValue: 0,
                duration: 250,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onClose();
        });
    };

    // 处理筛选选择
    const handleFilterSelect = (filterKey: string) => {
        setSelectedFilter(filterKey);
    };

    // 本地筛选任务数据
    const filteredTaskData = useMemo(() => {
        if (selectedFilter === 'all') {
            return taskData;
        }
        return taskData.filter((job) => job.type === selectedFilter);
    }, [taskData, selectedFilter]);

    const sections = useMemo(() => {
        return filteredTaskData.map((job) => {
            return {
                title: dayjs(job.createTime).format('YYYY-MM-DD HH:mm:ss'),
                type: job.type,
                data: [job],
                createTime: dayjs(job.createTime).format('YYYY-MM-DD HH:mm:ss'),
                jobData: job, // 保存原始job数据
            };
        });
    }, [filteredTaskData]);

    const renderEmptyState = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
                {loading ? '加载中...' : '暂无任务数据'}
            </Text>
        </View>
    );

    // 渲染筛选器
    const renderFilter = () => (
        <View style={styles.filterContainer}>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.filterScrollContent}
            >
                {filterOptions.map((option) => (
                    <TouchableOpacity
                        key={option.key}
                        style={[
                            styles.filterButton,
                            selectedFilter === option.key &&
                                styles.filterButtonActive,
                        ]}
                        onPress={() => handleFilterSelect(option.key)}
                    >
                        <Text
                            style={[
                                styles.filterButtonText,
                                selectedFilter === option.key &&
                                    styles.filterButtonTextActive,
                            ]}
                        >
                            {option.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </View>
    );

    return (
        <Modal
            visible={visible}
            animationType="none"
            transparent={true}
            onRequestClose={handleClose}
        >
            <View style={styles.overlay}>
                <Animated.View
                    style={[
                        styles.overlayBackground,
                        {
                            opacity: opacity,
                        },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.overlayTouchable}
                        activeOpacity={1}
                        onPress={handleClose}
                    />
                </Animated.View>
                <Animated.View
                    style={[
                        styles.drawer,
                        {
                            transform: [{ translateX }],
                            paddingTop: Platform.OS === 'ios' ? top : 0,
                        },
                    ]}
                >
                    {/* 头部 */}
                    <View style={styles.header}>
                        <Text style={styles.title}>今日任务</Text>
                        <TouchableOpacity
                            onPress={handleClose}
                            style={styles.closeButton}
                            hitSlop={{
                                top: 10,
                                bottom: 10,
                                left: 10,
                                right: 10,
                            }}
                        >
                            <LeftArrowIcon />
                        </TouchableOpacity>
                    </View>

                    {/* 筛选器 */}
                    {renderFilter()}

                    {/* 任务列表 */}
                    <View style={styles.listContainer}>
                        <SectionList
                            sections={sections}
                            renderItem={({ item, section }) => {
                                // 找到当前section在sections数组中的索引
                                const sectionIndex = sections.findIndex(
                                    (s) => s === section,
                                );

                                const itemView =
                                    section.type === 'AiCall' ? (
                                        <AICallItem item={item as TaskJob} />
                                    ) : (
                                        <View
                                            style={{
                                                flex: 1,
                                                marginRight: 12,
                                                borderRadius: 8,
                                                overflow: 'hidden',
                                            }}
                                        >
                                            {item.itemList?.map((item) => (
                                                <TaskListItem
                                                    item={
                                                        item as TaskItemWithTime
                                                    }
                                                    onViewResult={
                                                        handleViewResult
                                                    }
                                                />
                                            ))}
                                        </View>
                                    );
                                return (
                                    <View style={styles.timeLineItemContainer}>
                                        <View
                                            style={[
                                                styles.timeLineVerticalLine,
                                                {
                                                    opacity:
                                                        sectionIndex !==
                                                        sections.length - 1
                                                            ? 1
                                                            : 0,
                                                },
                                            ]}
                                        />

                                        {itemView}
                                    </View>
                                );
                            }}
                            renderSectionHeader={({ section: { title } }) => {
                                return (
                                    <View style={styles.sectionHeader}>
                                        <View style={styles.timeLineContainer}>
                                            <View style={styles.timeLineCircle}>
                                                <View
                                                    style={
                                                        styles.timeLineCircleInner
                                                    }
                                                />
                                            </View>
                                        </View>
                                        <View
                                            style={styles.sectionTimeContainer}
                                        >
                                            <Text style={styles.sectionDate}>
                                                {formatDateDisplay(title)}
                                            </Text>
                                            <Text style={styles.sectionTime}>
                                                {dayjs(title).format('H:mm')}
                                            </Text>
                                        </View>
                                    </View>
                                );
                            }}
                            keyExtractor={(item, index) =>
                                `${
                                    (item as any).jobId || (item as any).poiId
                                }_${index}`
                            }
                            showsVerticalScrollIndicator={false}
                            ListEmptyComponent={renderEmptyState}
                        />
                    </View>
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    overlayBackground: {
        flex: 1,
    },
    overlayTouchable: {
        flex: 1,
    },
    drawer: {
        backgroundColor: '#f2f2f2',
        width: screenWidth * 0.8,
        height: screenHeight,
        shadowColor: '#000',
        shadowOffset: {
            width: -2,
            height: 0,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        paddingTop: 20, // 增加顶部安全区域
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#222222',
    },
    closeButton: {
        padding: 4,
    },
    filterContainer: {
        paddingHorizontal: 16,
        marginBottom: 12,
    },
    filterScrollContent: {
        paddingRight: 16,
    },
    filterButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#fff',
        marginRight: 12,
        minWidth: 60,
        alignItems: 'center',
    },
    filterButtonActive: {
        backgroundColor: '#FFC300',
    },
    filterButtonText: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '400',
    },
    filterButtonTextActive: {
        color: '#FFFFFF',
        fontWeight: '500',
    },
    statsContainer: {
        paddingHorizontal: 16,
        marginBottom: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    statItem: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        backgroundColor: '#F9FAFC',
        borderRadius: 8,
        paddingVertical: 12,
        marginHorizontal: 4,
    },
    statValue: {
        fontSize: 22,
        fontWeight: 'bold',
        marginTop: 4,
        color: '#222',
    },
    statLabel: {
        fontSize: 14,
        color: '#666',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    sectionTime: {
        fontSize: 18,
        color: '#222222',
        fontFamily: 'PingFang SC',
        fontWeight: '600',
        lineHeight: 24,
        textAlign: 'center',
    },
    sectionTitle: {
        fontSize: 14,
        color: '#222222',
        fontWeight: '500',
    },
    timeLineItemContainer: {
        flexDirection: 'row',
    },
    timeLineContainer: {
        alignItems: 'center',
        width: 40, // 根据需要调整
        marginLeft: -16,
    },
    timeLineCircle: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: '#4021FF', // 根据需要调整
        marginBottom: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
    timeLineCircleInner: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#fff', // 根据需要调整
    },
    timeLineVerticalLine: {
        width: 2,
        backgroundColor: '#ddd', // 根据需要调整
        marginHorizontal: 18,
    },
    listContainer: {
        flex: 1,
        backgroundColor: '#f2f2f2',
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#999999',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 14,
        color: '#999999',
        marginTop: 12,
    },
    iconContainer: {
        padding: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
    sectionTimeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    sectionDate: {
        fontSize: 18,
        color: '#222222',
        fontFamily: 'PingFang SC',
        fontWeight: '600',
        lineHeight: 24,
        textAlign: 'center',
        marginRight: 8,
    },
});

export default TaskListDrawer;
