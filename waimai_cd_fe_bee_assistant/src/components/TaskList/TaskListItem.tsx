import {
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

import { TaskItem } from '../../api/taskApi';
import TWS from '../../TWS';

// 扩展TaskItem以包含任务类型信息
interface TaskListItemProps {
    /** 任务项数据 */
    item: TaskItem & {
        isUnread?: boolean;
        taskType?: 'PoiDiagnosis' | 'AiCall';
        agentName?: string;
        jobId?: string;
        poiNum?: number;
        taskName?: string;
    };
    /** 查看结果回调 */
    onViewResult: (item: TaskItem & { jobId?: string }) => void;
}

const AiCallTaskItem: React.FC<TaskListItemProps> = ({
    item,
    onViewResult,
}) => {
    const canViewResult = ['success', 'completed', 'fail', 'failed'].includes(
        item.status,
    );
    const isProcessing = ['init', 'running', 'pending'].includes(item.status);

    const getStatusInfo = () => {
        if (isProcessing) {
            return { text: '进行中', style: styles.processingStatusText };
        }
        if (['fail', 'failed'].includes(item.status)) {
            return { text: '已失败', style: styles.failedStatusText };
        }
        if (['success', 'completed'].includes(item.status)) {
            return { text: '已完成', style: styles.completedStatusText };
        }
        return { text: '', style: {} };
    };

    const statusInfo = getStatusInfo();

    return (
        <TouchableOpacity
            style={styles.aiCallContainer}
            onPress={() => onViewResult(item)}
            disabled={!canViewResult}
            activeOpacity={0.7}
        >
            <View style={styles.aiCallHeader}>
                <Text style={styles.aiCallTitle}>外呼任务</Text>
                {statusInfo.text ? (
                    <Text style={[styles.statusBase, statusInfo.style]}>
                        {statusInfo.text}
                    </Text>
                ) : null}
            </View>
            <View style={styles.aiCallContent}>
                <Text style={styles.aiCallText}>任务名称：{item.taskName}</Text>
                {item.poiNum ? (
                    <Text style={styles.aiCallText}>
                        外呼商家：{item.poiNum}家
                    </Text>
                ) : null}
                {item.agentName ? (
                    <Text style={styles.aiCallText}>
                        外呼agent：{item.agentName}
                    </Text>
                ) : null}
            </View>
        </TouchableOpacity>
    );
};

const PoiDiagnosisTaskItem: React.FC<TaskListItemProps> = ({
    item,
    onViewResult,
}) => {
    const canViewResult = ['success', 'completed', 'fail', 'failed'].includes(
        item.status,
    );
    const isProcessing = ['init', 'running', 'pending'].includes(item.status);

    const getStatusInfo = () => {
        if (isProcessing) {
            return { text: '进行中', style: styles.processingStatus };
        }
        if (['fail', 'failed'].includes(item.status)) {
            return { text: '已失败', style: styles.failedStatus };
        }
        if (['success', 'completed'].includes(item.status)) {
            return { text: '已完成', style: styles.completedStatus };
        }
        return null;
    };

    const renderAvatar = () => {
        if (item.poiAvatar) {
            return (
                <Image source={{ uri: item.poiAvatar }} style={styles.avatar} />
            );
        } else {
            return (
                <View style={[styles.avatar, styles.defaultAvatar]}>
                    <Text style={styles.avatarText}>🍽️</Text>
                </View>
            );
        }
    };

    const { title, subtitle } = {
        title: item.poiName,
        subtitle: `ID: ${item.poiId}`,
    };
    const statusInfo = getStatusInfo();

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={() => onViewResult(item)}
            disabled={!canViewResult}
            activeOpacity={0.7}
        >
            <View style={styles.avatarContainer}>
                {renderAvatar()}
                {item.isUnread && <View style={styles.unreadDot} />}
            </View>

            <View style={styles.content}>
                <Text
                    style={[styles.title, isProcessing && styles.disabledTitle]}
                    numberOfLines={1}
                >
                    {title}
                </Text>
                <Text style={styles.subtitle}>{subtitle}</Text>
            </View>

            {statusInfo ? (
                <View style={styles.statusContainer}>
                    <Text style={[styles.statusText, statusInfo.style]}>
                        {statusInfo.text}
                    </Text>
                </View>
            ) : null}
            {canViewResult ? (
                <Icon type="right" size={16} style={{ tintColor: '#CCCCCC' }} />
            ) : null}
        </TouchableOpacity>
    );
};

const TaskListItem: React.FC<TaskListItemProps> = (props) => {
    if (props.item.taskType === 'AiCall') {
        return <AiCallTaskItem {...props} />;
    }
    return <PoiDiagnosisTaskItem {...props} />;
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        backgroundColor: '#ffffff',
        flex: 1,
    },
    avatarContainer: {
        marginRight: 12,
        position: 'relative',
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 6,
        backgroundColor: '#f0f0f0',
    },
    defaultAvatar: {
        backgroundColor: '#FFF2E6',
        alignItems: 'center',
        justifyContent: 'center',
    },
    avatarText: {
        fontSize: 20,
    },
    unreadDot: {
        position: 'absolute',
        top: -3,
        right: -3,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#FF4D4F',
    },
    content: {
        flex: 1,
        marginRight: 12,
    },
    title: {
        fontSize: 14,
        fontWeight: '400',
        color: '#222222',
        marginBottom: 4,
    },
    disabledTitle: {
        color: '#999999',
    },
    subtitle: {
        fontSize: 12,
        color: '#999999',
        lineHeight: 16,
    },
    statusContainer: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
        marginRight: 4,
    },
    statusText: {
        fontSize: 12,
    },
    processingStatus: {
        color: '#FAAD14',
    },
    completedStatus: {
        color: '#52C41A',
    },
    failedStatus: {
        color: '#FF4D4F',
    },
    aiCallContainer: {
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        padding: 12,
        marginBottom: 12,
    },
    aiCallHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    aiCallTitle: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222222',
    },
    aiCallContent: {
        // marginLeft: 28, // to align with title if there was an icon
    },
    aiCallText: {
        fontSize: 13,
        color: '#555555',
        lineHeight: 18,
    },
    statusBase: {
        fontSize: 12,
    },
    processingStatusText: {
        color: '#666666',
        ...TWS.button({
            borderWidth: 1,
            borderColor: '#E6E6E6',
            paddingHorizontal: 10,
            paddingVertical: 4,
            borderRadius: 12,
        }),
    },
    completedStatusText: {
        color: '#52C41A',
    },
    failedStatusText: {
        color: '#FF4D4F',
    },
});

export default TaskListItem;
