import { StyleSheet, Text, View, Image } from '@mrn/react-native';
import React from 'react';

import { TaskJob } from '../../api/taskApi';
import NetImage from '../../assets/images/homeRefactor';
import TWS from '../../TWS';

interface AICallItemProps {
    item: TaskJob;
}

const statusMap = {
    pending: '进行中',
    running: '进行中',
    completed: '已完成',
    failed: '已失败',
};
const statusColorMap = {
    pending: '#4A90E2',
    running: '#4A90E2',
    completed: '#7ED321',
    failed: '#D0021B',
};

const AICallItem: React.FC<AICallItemProps> = ({ item }) => {
    const statusText = statusMap[item.status] || '未知状态';
    const statusColor = statusColorMap[item.status] || '#999999';

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>外呼任务</Text>
                {item.status === 'running' && (
                    <View style={styles.statusContainer}>
                        <Image
                            source={NetImage.spark}
                            style={styles.statusIcon}
                        />

                        <Text
                            style={[styles.statusText, { color: statusColor }]}
                        >
                            {statusText}
                        </Text>
                    </View>
                )}
            </View>
            <View style={styles.content}>
                <Text style={styles.detailText}>任务名称: {item.jobName}</Text>
                <Text style={styles.detailText}>外呼商家: {item.poiNum}家</Text>
                {item.agentName && (
                    <Text style={styles.detailText}>
                        外呼agent: {item.agentName}
                    </Text>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        padding: 16,
        marginRight: 16,
        marginBottom: 12,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    title: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222222',
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        ...TWS.button({
            borderWidth: 1,
            borderColor: '#E6E6E6',
            paddingHorizontal: 8,
            paddingVertical: 2,
            borderRadius: 12,
        }),
    },
    statusIcon: {
        width: 12,
        height: 12,
        marginRight: 4,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '400',
    },
    content: {
        paddingLeft: 4,
    },
    detailText: {
        fontSize: 13,
        color: '#666666',
        lineHeight: 20,
    },
});

export default AICallItem;
