### 1. 外呼结果推送、查询-小蜜
- 沟通任务完成后，推送任务完成消息（PC端不需要）。
- 新增“外呼任务”分类。
- 任务队列支持筛选类型（全部、商家诊断、外呼任务），默认全部。
- - 示意图如图：./taskList.png
**任务列表接口定义：**

```typescript
interface TaskListResponse {
    code: number;           // 响应码，0表示成功
    msg: string;           // 错误信息
    data: {
        jobList: TaskJob[];
    };
}

interface TaskJob {
    type: 'PoiDiagnosis' | 'AiCall';    // 任务类型
    status: 'pending' | 'running' | 'completed' | 'failed';  // 任务状态
    jobId: string;                      // 任务ID，jobId+type确定唯一任务
    jobName: string;                    // 任务名称
    createTime: number;                 // 创建时间戳
    completeTime?: number;              // 完成时间戳
    poiNum: number;                     // 商家个数
    agentName?: string;                 // agent名称，type=AiCall时有值
    operationType?: '1' | '2';          // 操作类型：1-url跳转，2-继续提问
    content?: string;                   // 操作内容：url或提问query
    itemList?: TaskItem[];              // 子任务列表，仅商家诊断任务有值
}

interface TaskItem {
    poiId: number;          // 商家ID
    poiName: string;        // 商家名称
    poiAvatar: string;      // 商家头像
    status: string;         // 子任务状态
    abilityType: string;    // 能力类型
    operationType: string;  // 操作类型
    content: string;        // 内容
}
```

**饼图组件：**
- 支持展示外呼结果统计数据
- 示意图如图：./pieChart.png
- 饼图数据格式如下：

```typescript
interface PieChart {
    type: 'pieChart';
    insert: {
        pieChart: {
            title?: string;     // 图表标题
            data: {
                label: string;  // 标签名称
                value: number;  // 数值
                color?: string; // 自定义颜色
            }[];
            showLegend?: boolean;   // 是否显示图例，默认true
            showPercent?: boolean;  // 是否显示百分比，默认true
        };
    };
}
```

**UI规范：**

- 饼图直径：200px
- 图例位置：右侧垂直排列
- 颜色方案：使用美团品牌色系
- 支持点击图例切换显示/隐藏对应扇形

