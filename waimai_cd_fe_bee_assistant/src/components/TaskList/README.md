# 任务列表功能更新

根据PRD要求，对任务列表功能进行了全面更新，支持外呼任务和饼图展示。

## 主要更新内容

### 1. API接口更新 (`src/api/taskApi.ts`)

- 新增 `TaskType` 类型：支持 `'PoiDiagnosis'` 和 `'AiCall'` 两种任务类型
- 新增 `TaskStatus` 类型：支持 `'pending' | 'running' | 'completed' | 'failed'` 状态
- 更新 `TaskJob` 接口：符合PRD中的任务作业定义
- 新增 `getTaskList` 函数：支持按任务类型筛选
- 新增饼图相关接口：`PieChartData` 和 `PieChartMessage`

### 2. 任务列表抽屉更新 (`src/components/TaskList/TaskListDrawer.tsx`)

- **任务类型筛选**：新增筛选器，支持"全部"、"商家诊断"、"外呼任务"三种筛选
- **数据适配**：适配新的API数据结构
- **外呼任务支持**：支持外呼任务的特殊显示和交互逻辑
- **统计信息**：更新统计逻辑以支持新的任务状态

### 3. 任务列表项更新 (`src/components/TaskList/TaskListItem.tsx`)

- **任务类型识别**：根据任务类型显示不同的图标和信息
- **外呼任务样式**：外呼任务显示电话图标和特殊样式
- **状态显示优化**：支持更多任务状态的视觉反馈
- **Agent信息显示**：外呼任务显示Agent名称

### 4. 饼图组件优化 (`src/components/PieChart/PieChart.tsx`)

- **UI规范**：符合PRD要求的200px直径规范
- **美团品牌色系**：使用美团标准色彩方案
- **交互功能**：支持点击图例切换显示/隐藏扇形
- **百分比显示**：支持可选的百分比显示
- **图例位置**：右侧垂直排列

### 5. 消息类型扩展 (`src/types/message.ts`)

- 更新 `PieChartData` 接口：color属性改为可选
- 新增 `showPercent` 属性支持
- 更新 `Message` 联合类型：包含 `PieChartMessage` 和 `AICallRecordMessage`

### 6. 消息渲染更新 (`src/components/MessageBox/Answer/AnswerContent/textMessageUtils.tsx`)

- 更新饼图消息渲染逻辑
- 支持新的饼图属性：`showPercent`
- 使用默认值确保向后兼容

## 功能特性

### 任务筛选
- 支持按任务类型筛选：全部、商家诊断、外呼任务
- 实时筛选，无需刷新页面

### 外呼任务支持
- 专门的外呼任务图标和样式
- 支持Agent名称显示
- 支持URL跳转和继续提问两种操作类型

### 饼图展示
- 直径200px，符合UI规范
- 美团品牌色系
- 支持图例交互
- 支持百分比显示
- 适用于外呼结果统计等场景

### 状态管理
- 支持多种任务状态：pending、running、completed、failed
- 兼容旧状态：init、success、fail
- 视觉化状态反馈

## 使用示例

### 饼图消息格式

```typescript
const pieChartMessage: PieChartMessage = {
    type: 'pieChart',
    insert: {
        pieChart: {
            title: '外呼结果统计',
            data: [
                { label: '接听成功', value: 45, color: '#52C41A' },
                { label: '无人接听', value: 20, color: '#FFC300' },
                { label: '拒绝接听', value: 15, color: '#FF6B35' },
                { label: '号码错误', value: 10, color: '#FF4D4F' },
            ],
            showLegend: true,
            showPercent: true,
        },
    },
};
```

### 任务列表API调用

```typescript
// 获取所有任务
const allTasks = await getTaskList();

// 获取商家诊断任务
const diagnosisTasks = await getTaskList('PoiDiagnosis');

// 获取外呼任务
const aiCallTasks = await getTaskList('AiCall');
```

## 向后兼容性

- 保持对现有商家诊断任务的完全支持
- 兼容旧的任务状态值
- API接口向后兼容
- 消息类型向后兼容

## 测试建议

1. 测试任务类型筛选功能
2. 测试外呼任务的显示和交互
3. 测试饼图的交互功能
4. 测试新旧任务状态的兼容性
5. 测试消息中饼图的渲染