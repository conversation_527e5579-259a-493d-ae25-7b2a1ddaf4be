import { deviceEventEmitter } from '@mrn/mrn-utils';
import { useCallback, useEffect, useRef, useState } from 'react';
import { DeviceEventEmitter } from 'react-native';

import { getTaskStatus, recordPopup } from '../../../api/taskApi';
import { SOURCE } from '../../../types';

const sourceToTab = {
    [SOURCE.home]: 'Base',
    [SOURCE.wdcAdopt]: 'PoiCenter',
    [SOURCE.wdcNearby]: 'PoiCenter',
    [SOURCE.wdcAll]: 'PoiCenter',
    [SOURCE.wdcResponsible]: 'PoiCenter',
    [SOURCE.wdcConcerned]: 'PoiCenter',
    [SOURCE.dove]: 'Homer',
    [SOURCE.tabWorkbench]: 'Workbench',
    [SOURCE.tabMine]: 'Mine',
};

const useNotify = (visible: boolean, source: SOURCE) => {
    const [isShowTaskReminder, setShowTaskReminder] = useState(false);
    const [currentNotifyText, setCurrentNotifyText] =
        useState<string>('商家诊断任务已全部完成'); // 当前提醒文本
    const bubbleTimer = useRef(null); // 气泡自动隐藏定时器
    const pollingTimer = useRef(null); // 轮询定时器
    const isPollingPaused = useRef(false); // 轮询是否暂停

    // 队列管理状态
    const notifyQueue = useRef<string[]>([]); // 提醒队列
    const isProcessingQueue = useRef(false); // 是否正在处理队列
    const queueTimer = useRef<NodeJS.Timeout | null>(null); // 队列处理定时器

    // 添加到提醒队列
    const addToNotifyQueue = useCallback((jobNames: string[]) => {
        if (jobNames && jobNames.length > 0) {
            notifyQueue.current = [...notifyQueue.current, ...jobNames];
            if (!isProcessingQueue.current) {
                processNotifyQueue();
            }
        }
    }, []);

    // 处理提醒队列
    const processNotifyQueue = useCallback(() => {
        if (notifyQueue.current.length === 0) {
            isProcessingQueue.current = false;
            return;
        }

        isProcessingQueue.current = true;
        const nextNotify = notifyQueue.current.shift();

        if (nextNotify) {
            setCurrentNotifyText(`${nextNotify}任务已完成`);
            setShowTaskReminder(true);

            // 2秒后处理下一个提醒
            queueTimer.current = setTimeout(() => {
                setShowTaskReminder(false);
                // 等待气泡隐藏动画完成后处理下一个
                setTimeout(() => {
                    processNotifyQueue();
                }, 300);
            }, 2000);
        }
    }, []);

    // 清理队列定时器
    const clearQueueTimer = useCallback(() => {
        if (queueTimer.current) {
            clearTimeout(queueTimer.current);
            queueTimer.current = null;
        }
    }, []);

    // 隐藏TaskReminderBubble并发起recordPopup请求
    const hideTaskReminder = useCallback(async () => {
        if (isShowTaskReminder) {
            try {
                await recordPopup();
            } catch (error) {
                console.error('Failed to record popup:', error);
            }
            setShowTaskReminder(false);

            // 如果正在处理队列，停止队列处理
            if (isProcessingQueue.current) {
                clearQueueTimer();
                isProcessingQueue.current = false;
                notifyQueue.current = []; // 清空队列
            }
        }
    }, [isShowTaskReminder, clearQueueTimer]);

    const fetchRunningJob = useCallback(async () => {
        try {
            const data = await getTaskStatus();

            // 优先处理 notifyJobNames 队列
            if (data?.notifyJobNames && data.notifyJobNames.length > 0) {
                addToNotifyQueue(data.notifyJobNames);
            }
            // 如果没有队列处理且有 needToClick，显示默认提醒
            else if (data?.needToClick && !isProcessingQueue.current) {
                setCurrentNotifyText('商家诊断任务已全部完成');
                setShowTaskReminder(true);
            }
        } catch (error) {
            console.error('Failed to fetch running job:', error);
        }
    }, [addToNotifyQueue]);

    // 开始轮询
    const startPolling = useCallback(() => {
        if (!visible) {
            return;
        }
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
        }
        pollingTimer.current = setInterval(() => {
            fetchRunningJob();
        }, 10000);
        isPollingPaused.current = false;
    }, [fetchRunningJob, visible]);

    // 暂停轮询
    const pausePolling = useCallback(() => {
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
            pollingTimer.current = null;
        }
        isPollingPaused.current = true;
    }, []);

    // 轮询 fetchRunningJob
    useEffect(() => {
        fetchRunningJob(); // 初始调用
        startPolling(); // 开始轮询

        return () => {
            if (pollingTimer.current) {
                clearInterval(pollingTimer.current);
            }
        };
    }, [fetchRunningJob, startPolling]);

    // 气泡展示5s后自动隐藏
    useEffect(() => {
        if (isShowTaskReminder) {
            // 清除之前的定时器
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }

            // 设置5s后自动隐藏
            bubbleTimer.current = setTimeout(() => {
                hideTaskReminder();
            }, 5000);
        }

        return () => {
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
        };
    }, [isShowTaskReminder, hideTaskReminder]);

    // 从首页直接切换到其他bundle的场景
    useEffect(() => {
        if (!visible) {
            return;
        }
        const sub1 = deviceEventEmitter.addListener(
            'containerViewDidAppear',
            () => {
                // 如果有暂停的计时则开始计时
                if (
                    isPollingPaused.current &&
                    currentTab === sourceToTab[source]
                ) {
                    startPolling();
                }
            },
        );
        const sub2 = deviceEventEmitter.addListener(
            'containerViewDidDisappear',
            () => {
                // 如果有正在进行的计时则暂停计时
                if (pollingTimer.current) {
                    pausePolling();
                }
            },
        );
        return () => {
            sub1.remove();
            sub2.remove();
        };
    }, [startPolling, pausePolling, visible]);

    const [currentTab, setCurrentTab] = useState('Base');

    useEffect(() => {
        const sub = DeviceEventEmitter.addListener(
            'BEE_BASE_TAB_CHANGE',
            (tab) => {
                setCurrentTab(tab);
            },
        );
        return () => {
            sub.remove();
        };
    }, []);

    useEffect(() => {
        if (currentTab === sourceToTab[source]) {
            startPolling();
        } else {
            pausePolling();
        }
    }, [currentTab]);

    // 清理所有定时器
    useEffect(() => {
        return () => {
            clearQueueTimer();
            if (pollingTimer.current) {
                clearInterval(pollingTimer.current);
            }
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
        };
    }, [clearQueueTimer]);

    return {
        isShowTaskReminder,
        hideTaskReminder,
        currentNotifyText,
    };
};

export default useNotify;
