import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';

import AICallRecord from '../AICallRecord';

describe('AICallRecord Component', () => {
    const mockData = {
        content: [
            {
                jobName: '测试外呼任务',
                status: 'running' as const,
                descriptions: [
                    { label: '外呼商家', value: '100家' },
                    { label: '创建时间', value: '2024-01-15 10:30:00' },
                ],
                button: {
                    text: '查看详情',
                    action: 'submitQuestion',
                    question: '查看详细',
                    type: 'normal' as const,
                },
                createTime: 1705292200000,
                progress: 65,
            },
        ],
        extendButtonName: '展开',
        showNum: 1,
    } as const;

    it('should render correctly with basic props', () => {
        const { getByText } = render(<AICallRecord data={mockData as any} />);

        expect(getByText('测试外呼任务')).toBeTruthy();
        expect(getByText('进行中')).toBeTruthy();
        expect(getByText('外呼商家：')).toBeTruthy();
        expect(getByText('100家')).toBeTruthy();
    });

    it('should show correct status for different states', () => {
        const { rerender, getByText } = render(
            <AICallRecord data={mockData as any} />,
        );
        expect(getByText('进行中')).toBeTruthy();

        const successData = {
            ...mockData,
            content: [{ ...mockData.content[0], status: 'success' as const }],
        };
        rerender(<AICallRecord data={successData as any} />);
        expect(getByText('已完成')).toBeTruthy();

        const failedData = {
            ...mockData,
            content: [{ ...mockData.content[0], status: 'fail' as const }],
        };
        rerender(<AICallRecord data={failedData as any} />);
        expect(getByText('失败')).toBeTruthy();
    });

    it('should toggle expand/collapse functionality', () => {
        const { getByText } = render(
            <AICallRecord
                data={
                    { ...mockData, extendButtonName: '展开', showNum: 0 } as any
                }
            />,
        );

        // 点击展开按钮
        fireEvent.press(getByText(/展开|收起/));
        expect(getByText(/展开|收起/)).toBeTruthy();
    });
});
