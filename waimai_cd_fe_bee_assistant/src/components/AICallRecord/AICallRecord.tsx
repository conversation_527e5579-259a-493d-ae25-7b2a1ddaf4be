import { openPage } from '@mfe/bee-foundation-utils';
import { View, Text, TouchableOpacity, StyleSheet, Image } from '@mrn/react-native';
import React, { useMemo, useState } from 'react';
import Svg, { Defs, LinearGradient, Stop, Text as SvgText } from 'react-native-svg';

import { useSendMessage } from '../../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../../types';
import { AICallRecordItem, ActionCardButton } from '../../types/message';
import { Icon } from '@roo/roo-rn';
import NetImages from '../../assets/images/homeRefactor';

interface AICallRecordProps {
    data: {
        content: AICallRecordItem[];
        extendButtonName: string;
        showNum: number;
    };
}

// 紫色渐变文字组件
const GradientText: React.FC<{ text: string }> = ({ text }) => {
    return (
        <Svg height="16" width="50">
            <Defs>
                <LinearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <Stop offset="0%" stopColor="#8B5CF6" stopOpacity="1" />
                    <Stop offset="100%" stopColor="#A855F7" stopOpacity="1" />
                </LinearGradient>
            </Defs>
            <SvgText
                fill="url(#purpleGradient)"
                fontSize="12"
                fontWeight="500"
                x="0"
                y="12"
            >
                {text}
            </SvgText>
        </Svg>
    );
};

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const [expanded, setExpanded] = useState(false);
    const { send } = useSendMessage();

    const { content, extendButtonName, showNum } = data;

    const displayItems = useMemo(() => {
        if (!showNum || expanded) {
            return content;
        }
        return content.slice(0, showNum);
    }, [content, expanded, showNum]);

    const showExpand =
        Boolean(extendButtonName) && content.length > (showNum || 0);

    const handleButtonPress = (btn: ActionCardButton) => {
        if (btn.action === 'submitQuestion' && btn.question) {
            send(btn.question, EntryPointType.USER, EntryPoint.action_card);
            return;
        }
        if (btn.action === 'openAICallModal') {
            // 交给 ActionCard 的弹窗触发；若需要在此处触发，可引入 useAICallModal
            return;
        }
        if (btn.url) {
            openPage(btn.url);
        }
    };

    const renderStatus = (status: AICallRecordItem['status']) => {
        switch (status) {
            case 'init':
                return { text: '待开始', color: '#faad14', bg: '#fff7e6' };
            case 'running':
                return { text: '进行中', color: '#1890ff', bg: '#e6f7ff' };
            case 'success':
                return { text: '已完成', color: '#52c41a', bg: '#f6ffed' };
            case 'fail':
                return { text: '失败', color: '#ff4d4f', bg: '#fff2f0' };
            default:
                return { text: '未知', color: '#d9d9d9', bg: '#f5f5f5' };
        }
    };

    return (
        <View style={styles.container}>
            {displayItems.map((item, idx) => {
                const s = item.status !== 'running' ? renderStatus(item.status) : null;
                return (
                    <View key={idx} style={styles.card}>
                        <View style={{ flex: 1 }}>
                            <View style={styles.header}>
                                <View style={styles.titleRow}>
                                    <Text style={styles.taskName} numberOfLines={1}>
                                        {item.jobName}
                                    </Text>
                                    {item.status === 'running' ? (
                                        <View style={styles.runningStatusContainer}>
                                            <Image
                                                source={{ uri: NetImages.star }}
                                                style={styles.starIcon}
                                            />
                                            <GradientText text="进行中" />
                                        </View>
                                    ) : null}
                                </View>
                            </View>
                            <View>
                                {item.descriptions?.map((d, i) => (
                                    <View style={styles.descRow} key={i}>
                                        <Text style={styles.descLabel}>
                                            {d.label}：
                                        </Text>
                                        <Text style={styles.descValue}>
                                            {d.value}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>

                        {item.status !== 'running' && item.button ? (
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    item.button.type === 'primary'
                                        ? styles.primaryButton
                                        : styles.normalButton,
                                ]}
                                onPress={() => handleButtonPress(item.button)}
                                activeOpacity={0.8}
                            >
                                <Text style={styles.buttonText}>
                                    {item.button.text}
                                </Text>
                            </TouchableOpacity>
                        ) : null}
                    </View>
                );
            })}

            {showExpand ? (
                <TouchableOpacity
                    style={styles.expandButton}
                    onPress={() => setExpanded((v) => !v)}
                >
                    <Text style={styles.expandText}>
                        共{content.length} {expanded ? '收起' : '展开'}
                    </Text>
                    
                    <Icon type={'down'} size={10} style={                            [styles.expandIcon, expanded && styles.expandIconRotated,]
}/>
                </TouchableOpacity>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 4,
    },
    card: {
        backgroundColor: '#F5F6FA',
        borderRadius: 12,
        padding: 12,
        marginBottom: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    header: {
        marginBottom: 4,
    },
    titleRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    taskName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2329',
        marginRight: 8,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    descRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginTop: 4,
    },
    descLabel: {
        color: '#86909c',
        fontSize: 12,
        marginRight: 6,
        minWidth: 60,
    },
    descValue: {
        color: '#86909c',
        fontSize: 12,
    },
    button: {
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 14,
    },
    primaryButton: {
        backgroundColor: '#FFDD00',
        borderWidth: 1,
        borderColor: '#FFDD00',
    },
    normalButton: {
        backgroundColor: '#ffffff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    buttonText: {
        color: '#1f2329',
        fontSize: 12,
        fontWeight: '500',
    },
    expandButton: {
        alignSelf: 'center',
        paddingVertical: 6,
        paddingHorizontal: 8,
        flexDirection: 'row',
        alignItems: 'center',
    },
    expandText: {
        color: '#86909c',
        fontSize: 12,
    },
    expandIcon: {
        marginLeft: 4,
        justifyContent: 'center',
        alignItems: 'center',
    },
    expandIconRotated: {
        transform: [{ rotate: '180deg' }],
    },
    expandArrow: {
        fontSize: 12,
        color: '#86909c',
    },
    runningStatusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    starIcon: {
        width: 12,
        height: 12,
        marginRight: 4,
    },
});

export default AICallRecord;
