import { apiCaller, BFFResponse } from '@mfe/cc-api-caller-bee';

/**
 * 任务状态接口响应
 */
export interface TaskStatusResponse {
    /** 正在运行中的任务个数 */
    runnings: number;
    /** 是否有需要点击查看的任务 */
    needToClick: boolean;
    /** 需要提醒的任务名称列表 */
    notifyJobNames?: string[];
}

/**
 * 任务类型
 */
export type TaskType = 'PoiDiagnosis' | 'AiCall';

/**
 * 任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed';

/**
 * 任务项接口 - 根据PRD更新
 */
export interface TaskItem {
    poiId: number; // 商家ID
    poiName: string; // 商家名称
    poiAvatar: string; // 商家头像
    status: string; // 子任务状态
    abilityType: string; // 能力类型
    operationType: string; // 操作类型
    content: string; // 内容
}

/**
 * 任务作业接口 - 根据PRD定义
 */
export interface TaskJob {
    type: TaskType; // 任务类型
    status: TaskStatus; // 任务状态
    jobId: string; // 任务ID，jobId+type确定唯一任务
    jobName: string; // 任务名称
    createTime: number; // 创建时间戳
    completeTime?: number; // 完成时间戳
    poiNum: number; // 商家个数
    agentName?: string; // agent名称，type=AiCall时有值
    operationType?: '1' | '2'; // 操作类型：1-url跳转，2-继续提问
    content?: string; // 操作内容：url或提问query
    itemList?: TaskItem[]; // 子任务列表，仅商家诊断任务有值
}

/**
 * 任务组接口 - 保持向后兼容
 */
export interface TaskGroup {
    /** 任务类型 */
    type: string;
    /** 创建时间 */
    createTime: string;
    /** 任务名称 */
    jobName: string;
    itemList: TaskItem[];
}

/**
 * 任务列表接口响应 - 根据PRD更新
 */
export interface TaskListResponse {
    code: number; // 响应码，0表示成功
    msg: string; // 错误信息
    data: {
        jobList: TaskJob[];
    };
}

/**
 * 饼图数据接口 - 根据PRD定义
 */
export interface PieChartData {
    label: string; // 标签名称
    value: number; // 数值
    color?: string; // 自定义颜色
}

/**
 * 饼图消息接口 - 根据PRD定义
 */
export interface PieChartMessage {
    type: 'pieChart';
    insert: {
        pieChart: {
            title?: string; // 图表标题
            data: PieChartData[];
            showLegend?: boolean; // 是否显示图例，默认true
            showPercent?: boolean; // 是否显示百分比，默认true
        };
    };
}

/**
 * API响应基础结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data?: T;
}

/**
 * 获取任务状态
 * @returns 任务状态数据
 */
export const getTaskStatus = async (): Promise<TaskStatusResponse | null> => {
    try {
        const res: ApiResponse<TaskStatusResponse> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/poiDiagnosis/runningToday',
            {},
            { silent: true },
        );

        if (res.code === 0 && res.data) {
            return res.data;
        }

        return null;
    } catch (error) {
        console.error('获取任务状态失败:', error);
        return null;
    }
};

/**
 * 获取任务列表 - 支持类型筛选
 * @param taskType 任务类型筛选，不传则返回全部
 * @returns 任务列表数据
 */
export const getTaskList = async (
    taskType?: TaskType,
): Promise<TaskListResponse | null> => {
    try {
        const params = taskType ? { type: taskType } : {};
        const res: ApiResponse<{ jobList: TaskJob[] }> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/listToday',
            params,
            { silent: true },
        );

        if (res.code === 0 && res.data) {
            return {
                code: res.code,
                msg: res.msg,
                data: res.data,
            };
        }

        return null;
    } catch (error) {
        console.error('获取任务列表失败:', error);
        return null;
    }
};

/**
 * 记录弹窗行为
 * @returns 记录结果
 */
export const recordPopup = async (): Promise<BFFResponse<any>> => {
    return apiCaller.get(
        '/bee/v2/bdaiassistant/job/recordPopup',
        {},
        { silent: true },
    );
};
