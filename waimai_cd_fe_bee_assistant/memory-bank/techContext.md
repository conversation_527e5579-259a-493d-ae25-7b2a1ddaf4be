## 技术上下文（Tech Context）

### 运行与工具链
- Node：16.20.2（Volta 固定）
- Yarn：1.22.22
- TypeScript：^5.8.3（建议启用严格模式）

### 主要依赖（节选）
- `@mrn/react-native`: 3.0.26（RN 0.67 基线）
- `zustand`: ^4.4.6（全局状态管理）
- `immer`: ^10.0.3（不可变更新）
- `ahooks`: ^3.7.8（包含 `useRequest` 等）
- `@roo/roo-rn`: ^1.0.6（组件库）
- 其它：`dayjs`、`lodash`、`echarts`、`react-native-webview` 等

### 脚本
- 开发：`yarn start` / `mrn start`
- 主入口：`mrn start --entryName bee-assistant-main --port 4782`
- 构建：`yarn build` / `mrn build`
- 代码质量：`yarn lint` / `yarn fix` / `husky` + `lint-staged`

### 代码规范
- TypeScript 严格类型；避免 `any`；组件 Props 使用明确接口。
- `StyleSheet.create()` 统一样式；遵循现有命名与组织方式。
- 使用 `Condition` 组件进行条件渲染。

### 接口（来自 PRD）
- 获取任务状态：`GET /bee/v2/bdaiassistant/common/task/list`
- 获取任务列表：`GET /bee/v2/bdaiassistant/job/list`
- 响应类型参考：`apiSpec/autoGeneratedChat.d.ts`（后续对齐落地）

### 联调与 Mock
- Mock 开关：`src/hooks/mock/useMockMode.ts`
- Mock 数据：`src/hooks/mock/mockData.ts`
- CI/提交：本地提交会触发 `husky` + `lint-staged` 执行 ESLint 检查；当前仓库仅阻塞 error，warning 不阻塞。


