## 系统模式与架构（System Patterns）

### 架构风格
- 函数组件 + Hooks；强类型 TypeScript；组件模块化与可重用。
- 数据管理以组件局部状态为主，跨模块共享使用 `zustand`。
- 不可变状态更新使用 `immer`，提升可读性与安全性。

### 数据流与状态
- 任务状态与列表：基于请求层（`ahooks` 的 `useRequest` 或自定义轮询）
  - 轮询间隔：2s（可配置），错误自动重试策略谨慎设置，避免抖动。
  - 数据进入局部/全局状态，再驱动 UI（抽屉/列表/徽标）。

### UI 与交互
- 组件：`@roo/roo-rn` 与 `@mrn/react-native` 基础组件。
- 样式：`StyleSheet.create()`，统一命名与TWS工具函数。
- 条件渲染：`Condition` 组件。
- 长列表：必要时虚拟化与分页（如 `react-native-draggable-flatlist` 仅在需要时引入）。

### 性能与可用性
- 合理 `useEffect` 依赖；避免渲染中创建新对象/函数；对频繁事件 `useDebounceFn`。
- 文案与空态：异常时优雅降级，保持可操作路径。

### 监控与埋点
- 使用 `LXTrack` 按项目规范记录关键行为（入口点击、列表曝光、结果跳转）。
- 使用 `TraceUtil` 记录关键路径性能（加载、轮询、渲染）。

### 平台兼容
- `Platform.select` 处理差异；iOS/Android 键盘事件差异；`SafeAreaInsets` 合理使用。

### 错误处理
- 请求失败：展示降级内容而非空白；必要时 toast/提示；错误上报。
- 数据异常：保护性判空与类型收窄；保持 UI 稳定。


### Mock 与联调
- 模式开关：通过 `src/hooks/mock/useMockMode.ts` 控制是否启用 mock；默认关闭，仅在联调/演示时启用。
- 数据来源：`src/hooks/mock/mockData.ts` 维护关键接口的本地数据，便于快速验证与回归。
- 实践建议：
  - 统一从 hook 或工具函数读取 mock 状态，避免分散判定。
  - mock 与真实接口的类型需保持一致，避免 `any`，减少切换成本。
  - 在提交前保持 mock 关闭，必要时通过配置开关或灰度控制。


