## 当前活动上下文（Active Context）

### 当前焦点
- 任务列表功能：入口按钮（含运行中任务徽标）、抽屉面板、状态分类、2s 轮询、结果查看、与聊天模块联动触发。
- AI 外呼联调：通过可切换的 mock 模式保障联调效率与稳定性。

### 最近变更
- 建立 Memory Bank 文档基座。
- 明确技术栈与依赖、接口路径与关键流程。
- 更新 mock 模式与数据（`src/hooks/mock/useMockMode.ts`、`src/hooks/mock/mockData.ts`），支持 AI 外呼联调；已提交并推送至远端分支 `feature/AI外呼`。

### 下一步
- 与聊天模块打通“自动发起问题查询”的触发链路（任务点击 → 关闭抽屉 → 发送消息）。
- 完善异常/降级处理与空状态样式。
- 增加埋点（入口点击、列表曝光、任务点击、结果查看）。
- 创建 PR，合并 `feature/AI外呼` 相关改动；继续完善外呼相关埋点与异常兜底。

### 决策与考虑
- 轮询 vs 推送：当前采用 2s 轮询，后续如有推送能力可切换或混合。
- 抽屉宽度与适配：遵循设计建议（600px），按不同设备适配。
- 类型定义：与 `apiSpec/autoGeneratedChat.d.ts` 对齐，消除 any。
- Mock 策略：保留 `useMockMode` 开关以便在真实接口与 Mock 之间快速切换，降低联调成本与回归风险。


