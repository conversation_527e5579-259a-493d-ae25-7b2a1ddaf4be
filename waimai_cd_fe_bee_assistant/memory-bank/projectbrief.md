## 项目概览（Project Brief）

### 名称
- **BEE Assistant（蜂窝诊断助手）** — MRN 模块（@mrn/react-native 3.0.26）

### 背景与目标
- **背景**: 为提升用户对诊断任务执行过程的可见性与可控性，需要在现有系统中新增“任务列表”能力。
- **总体目标**:
  - 提供任务状态的实时查看能力
  - 支持任务结果的快速查看
  - 提升用户对任务执行过程的感知度
  - 优化整体操作流程与体验

### 范围
- 客户端：MRN（React Native 0.67 基线），与现有 BEE 模块集成。
- 功能域：任务列表入口、抽屉面板、状态分类与轮询、结果查看与聊天联动。
- 非目标（当期不覆盖）：后端任务调度策略调整、服务端推送通道建设。

### 关键成功指标（KPI）
- 任务状态查询的成功率与响应时延达标（轮询 2s 内直观更新）。
- 任务结果查看的转化率提升（从列表项直达结果/自动发问）。
- 错误/异常的可感知与降级（出现异常不留白）。

### 角色与干系人
- **使用者**: 业务运营、研发及相关一线同学。
- **维护者**: MRN 客户端团队；联动后端任务/聊天服务团队。

### 参考文档
- PRD：`prd/任务列表功能PRD.md`


