# 项目简介

## 项目概述

**项目名称**: 外卖 CRM BD 服务系统 (waimai_cd_fe_crm_bdservice)
**项目类型**: React + TypeScript 企业级 Web 应用
**主要功能**: 知识库管理系统 + AI 外呼助手

## 核心业务模块

### 1. 知识库管理系统

-   **知识库首页**: 展示知识库统计数据和管理入口
-   **知识上传**: 多步骤知识文档上传流程（上传 → 质检 → 预览 → 入库）
-   **知识详情**: 知识库内容查看和管理
-   **标准问管理**: FAQ 和标准问题管理

### 2. AI 外呼助手系统

-   **智能对话**: 基于知识库的 AI 对话系统
-   **外呼任务**: 创建和管理 AI 外呼任务
-   **任务监控**: 外呼任务执行状态和结果查看
-   **蜂窝诊断**: 商家诊断和数据分析工具

### 3. 数据展示和分析

-   **饼图组件**: 外呼结果统计展示
-   **任务列表**: 支持多类型任务筛选和管理
-   **实时监控**: 任务执行进度实时更新

## 技术架构特点

-   **微前端架构**: 使用 Qiankun 支持模块化开发
-   **现代化技术栈**: React 18 + TypeScript + Vite
-   **企业级 UI**: Ant Design 5.x + 内部@roo 组件库
-   **状态管理**: Zustand 轻量级状态管理
-   **开发规范**: ESLint + Prettier + Husky 完整代码质量保障

## 项目目标

1. **提升 BD 效率**: 通过 AI 助手和知识库系统提升业务开发效率
2. **标准化管理**: 统一知识管理和外呼流程标准化
3. **数据驱动**: 通过数据分析和监控优化业务决策
4. **用户体验**: 现代化 UI 设计和流畅的用户交互体验

## 当前开发重点

-   **UI 改版 2.0**: 首页布局重构和技能分类展示优化
-   **外呼功能**: AI 外呼任务创建和结果展示功能完善
-   **任务管理**: 蜂窝诊断任务列表功能开发
-   **组件优化**: ActionCard、PieChart 等业务组件增强
