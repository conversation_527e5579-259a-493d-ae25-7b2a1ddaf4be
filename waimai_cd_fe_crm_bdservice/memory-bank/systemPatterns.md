# 系统架构与设计模式

## 整体架构设计

### 微前端架构

-   **框架**: 基于 Qiankun 的微前端架构
-   **模块划分**: 按业务功能划分独立模块
-   **模块通信**: 通过事件总线和共享状态进行模块间通信
-   **独立部署**: 各模块可独立开发、测试和部署

### 前端架构分层

```
┌─────────────────────────────────────┐
│           UI层 (Pages/Components)    │
├─────────────────────────────────────┤
│           业务逻辑层 (Hooks/Services) │
├─────────────────────────────────────┤
│           状态管理层 (Zustand)        │
├─────────────────────────────────────┤
│           网络请求层 (API Caller)     │
├─────────────────────────────────────┤
│           工具层 (Utils/Constants)    │
└─────────────────────────────────────┘
```

## 核心设计模式

### 1. 组件设计模式

#### 容器组件模式

```typescript
// 页面级容器组件负责数据获取和状态管理
const KnowledgeHomePage: React.FC = () => {
    const { data, loading } = useRequest(fetchKnowledgeList);
    return <KnowledgeHomeView data={data} loading={loading} />;
};

// 展示组件负责UI渲染
const KnowledgeHomeView: React.FC<Props> = ({ data, loading }) => {
    return <div>...</div>;
};
```

#### 高阶组件模式

```typescript
// Config组件为页面提供统一配置
const withConfig = WrappedComponent => {
    return props => (
        <Config>
            <WrappedComponent {...props} />
        </Config>
    );
};
```

### 2. 状态管理模式

#### Zustand Store 设计

```typescript
// 按功能模块划分Store
interface AiStore {
    // 状态
    messages: Message[];
    sessionId: string;

    // 操作
    appendMessage: (message: Message) => void;
    setSessionId: (id: string) => void;
}

// 使用immer确保状态不可变性
const useAiStore = create<AiStore>(set => ({
    messages: [],
    appendMessage: message =>
        set(
            produce(state => {
                state.messages.push(message);
            }),
        ),
}));
```

### 3. 网络请求模式

#### API Caller 统一封装

```typescript
// 统一的API调用方式
const fetchData = async (params: RequestParams) => {
    const res = await apiCaller.send('/api/endpoint', params);
    if (res.code !== 0) {
        return; // 统一错误处理
    }
    return res.data;
};
```

#### 类型安全的 API 调用

```typescript
// 使用APISpec确保类型安全
type ApiResponse = APISpec['/api/endpoint']['response'];
type ApiRequest = APISpec['/api/endpoint']['request'];
```

## 关键技术决策

### 1. 技术栈选择

#### 前端框架: React 18

-   **选择原因**: 生态成熟、团队熟悉、性能优异
-   **关键特性**: 并发特性、Suspense、自动批处理

#### 构建工具: Vite

-   **选择原因**: 开发体验好、构建速度快、配置简单
-   **优势**: HMR 快速、ESBuild 编译、插件生态

#### UI 框架: Ant Design 5.x + @roo/roo

-   **Ant Design**: 企业级 UI 组件，设计规范统一
-   **@roo/roo**: 内部组件库，满足特定业务需求
-   **组合使用**: 优先使用 Ant Design，特殊需求使用内部组件

### 2. 状态管理: Zustand

-   **选择原因**: 轻量级、TypeScript 友好、学习成本低
-   **优势**: 无需 Provider、支持中间件、调试方便
-   **使用场景**: 全局状态、跨组件通信、持久化状态

### 3. 样式方案: SCSS + CSS Modules

-   **SCSS**: 提供变量、嵌套、混入等高级特性
-   **CSS Modules**: 避免样式冲突，支持局部作用域
-   **BEM 命名**: 保持样式命名规范性

## 组件架构设计

### 1. 页面组件结构

```
pages/
├── knowledge/
│   ├── home/           # 知识库首页
│   │   ├── App.tsx     # 页面容器组件
│   │   ├── main.tsx    # 页面入口
│   │   └── style.scss  # 页面样式
│   ├── chat/           # AI对话页面
│   └── upload/         # 知识上传页面
```

### 2. 通用组件设计

```
components/
├── business/           # 业务组件
│   ├── KnowledgeCard/  # 知识库卡片
│   ├── TaskList/       # 任务列表
│   └── MessageContent/ # 消息内容
├── common/             # 通用组件
│   ├── Condition/      # 条件渲染
│   ├── Title/          # 标题组件
│   └── Charts/         # 图表组件
```

### 3. Hook 设计模式

```typescript
// 业务逻辑封装到自定义Hook
const useKnowledgeList = () => {
    const { data, loading, error } = useRequest(fetchKnowledgeList);

    const refresh = useCallback(() => {
        // 刷新逻辑
    }, []);

    return { data, loading, error, refresh };
};

// 组件中使用
const KnowledgePage = () => {
    const { data, loading, refresh } = useKnowledgeList();
    // ...
};
```

## 数据流设计

### 1. 单向数据流

```
User Action → Event Handler → State Update → UI Re-render
```

### 2. 异步数据处理

```typescript
// 使用SWR进行数据缓存和同步
const { data, error, mutate } = useSWR('/api/data', fetcher);

// 使用ahooks的useRequest处理异步请求
const { data, loading, run } = useRequest(fetchData, {
    manual: true,
    onSuccess: result => {
        // 成功回调
    },
});
```

### 3. 实时数据更新

```typescript
// 轮询机制
const { data } = useRequest(fetchTaskStatus, {
    pollingInterval: 2000,
    pollingWhenHidden: false,
});

// WebSocket连接（如需要）
const useWebSocket = (url: string) => {
    // WebSocket连接和消息处理逻辑
};
```

## 性能优化策略

### 1. 组件优化

-   **React.memo**: 防止不必要的重渲染
-   **useMemo/useCallback**: 缓存计算结果和函数引用
-   **懒加载**: 路由级别的代码分割

### 2. 打包优化

-   **代码分割**: 按路由和功能分割代码
-   **Tree Shaking**: 移除未使用的代码
-   **资源压缩**: 图片和静态资源优化

### 3. 运行时优化

-   **虚拟滚动**: 大列表性能优化
-   **防抖节流**: 用户输入和请求优化
-   **缓存策略**: API 响应和计算结果缓存

## 错误处理机制

### 1. 统一错误处理

```typescript
// API级别错误处理
const apiCaller = {
    send: async (url, params) => {
        try {
            const response = await fetch(url, params);
            const result = await response.json();

            if (result.code !== 0) {
                // 统一错误提示
                message.error(result.msg || '请求失败');
                return;
            }

            return result.data;
        } catch (error) {
            // 网络错误处理
            message.error('网络异常，请稍后重试');
        }
    },
};
```

### 2. 组件级错误边界

```typescript
// 错误边界组件
class ErrorBoundary extends React.Component {
    componentDidCatch(error, errorInfo) {
        // 错误上报和处理
        console.error('Component Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return <ErrorFallback />;
        }
        return this.props.children;
    }
}
```

## 安全性设计

### 1. 输入验证

-   **表单验证**: 使用 Ant Design Form 的验证机制
-   **数据清洗**: 对用户输入进行清洗和转义
-   **类型检查**: TypeScript 提供编译时类型安全

### 2. 权限控制

-   **路由守卫**: 基于用户权限控制页面访问
-   **组件权限**: 根据权限显示/隐藏功能组件
-   **API 权限**: 后端接口权限验证

### 3. 数据安全

-   **敏感数据**: 避免在前端存储敏感信息
-   **HTTPS**: 确保数据传输安全
-   **CSP**: 内容安全策略防止 XSS 攻击
