# 技术环境与开发配置

## 技术栈详情

### 核心框架

-   **React**: 18.2.0 - 现代化 React 版本，支持并发特性
-   **TypeScript**: 4.9.3 - 类型安全的 JavaScript 超集
-   **Vite**: 5.1.8 - 快速的前端构建工具
-   **Node.js**: 18.20.5 (Volta 管理) - 运行时环境

### UI 框架和样式

-   **Ant Design**: 5.15.4 - 企业级 UI 设计语言和组件库
-   **@roo/roo**: 1.15.1-beta.2 - 内部 UI 组件库
-   **@roo/roo-plus**: 0.4.1-beta.2 - 内部 UI 组件库扩展
-   **SCSS**: 1.63.6 - CSS 预处理器
-   **@ant-design/icons**: 5.2.6 - Ant Design 图标库

### 状态管理和数据处理

-   **Zustand**: 5.0.1 - 轻量级状态管理库
-   **SWR**: 2.3.3 - 数据获取和缓存库
-   **Immer**: 10.0.2 - 不可变数据结构
-   **ahooks**: 3.7.11 - React Hooks 工具库

### 网络请求和 API

-   **@mfe/cc-api-caller-pc**: 0.2.8 - 内部 API 调用工具
-   **Axios**: 1.6.8 - HTTP 客户端
-   **APISpec**: 自动生成的 API 类型定义

### 开发工具和构建

-   **ESLint**: 8.36.0 - 代码检查工具
-   **Prettier**: 2.7.1 - 代码格式化工具
-   **Husky**: 7.0.0 - Git hooks 管理
-   **lint-staged**: 13.2.2 - 暂存区文件检查
-   **Vitest**: 1.x - 单元测试框架

### 微前端和企业工具

-   **Qiankun**: vite-plugin-qiankun 1.0.15 - 微前端框架
-   **@mfe/bellwether-route**: 1.0.9 - 路由管理
-   **@mtfe/sso-web**: 2.4.1 - 单点登录集成

### 富文本和特殊功能

-   **Quill**: 2.0.2 - 富文本编辑器
-   **Monaco Editor**: 0.52.2 - 代码编辑器
-   **React Beautiful DND**: 13.1.1 - 拖拽功能
-   **ECharts**: 6.0.0 - 图表库

## 开发环境配置

### 环境要求

-   **Node.js**: 18.20.5 (通过 Volta 版本管理)
-   **Yarn**: 1.22.22 (推荐包管理器)
-   **操作系统**: 支持 macOS、Linux、Windows

### 项目启动

```bash
# 安装依赖
yarn install

# 启动开发服务器
yarn dev

# 构建生产版本
yarn build

# 预览生产构建
yarn preview
```

### 开发服务器配置

-   **端口**: 默认 Vite 配置
-   **API 代理**: VITE_API_PREFIX=/xianfu/api/bdservice
-   **环境变量**: DEPLOY_ENV=dev

### 代码质量工具

#### ESLint 配置

```json
{
    "extends": ["@typescript-eslint/recommended", "plugin:react/recommended", "plugin:prettier/recommended"],
    "plugins": ["@typescript-eslint", "react", "unused-imports"]
}
```

#### Prettier 配置

-   自动格式化通过 lint-staged 在 git commit 时执行
-   支持 TypeScript、JavaScript、JSON、Markdown 等文件类型

#### Git Hooks

```json
{
    "lint-staged": {
        "*.{js,jsx,less,md,json}": ["prettier --write"],
        "*.ts?(x)": ["bash -c tsc", "prettier --parser=typescript --write"]
    }
}
```

## 构建和部署

### Vite 构建配置

-   **SWC 编译器**: 快速 TypeScript/JavaScript 编译
-   **多页面应用**: vite-plugin-mpa 支持
-   **SVG 支持**: vite-plugin-svgr
-   **路径别名**: vite-tsconfig-paths
-   **HTML 模板**: vite-plugin-html-template

### 微前端配置

-   **主应用**: 基于 Qiankun 的主应用配置
-   **子应用**: 各功能模块作为独立的子应用
-   **通信机制**: 全局状态共享和事件总线
-   **Module Federation**: 动态远程模块加载，支持多环境配置
    -   **test 环境**: s3plus-corp.sankuai.com 静态资源域名
    -   **st 环境**: mss.vip.sankuai.com 静态资源域名
    -   **prod 环境**: mss.vip.sankuai.com 静态资源域名
    -   **自动环境判断**: 基于 DEPLOY_ENV/AWP_DEPLOY_ENV 环境变量

### 类型定义管理

-   **API 类型**: 通过 parse-yapi 自动生成
-   **同步机制**: sync-dts 命令同步类型定义
-   **类型安全**: 严格的 TypeScript 配置

## 性能优化配置

### 打包优化

-   **代码分割**: 动态导入和路由级别分割
-   **Tree Shaking**: 移除未使用代码
-   **资源优化**: 图片和静态资源压缩
-   **缓存策略**: 浏览器缓存和 CDN 配置

### 运行时优化

-   **虚拟滚动**: rc-virtual-list 处理大列表
-   **懒加载**: React.lazy 和 Suspense
-   **内存管理**: 及时清理事件监听器和定时器

## 测试配置

### 测试框架

-   **Vitest**: 快速的单元测试框架
-   **Testing Library**: React 组件测试
-   **jsdom**: DOM 环境模拟
-   **覆盖率**: @vitest/coverage-v8

### 测试命令

```bash
# 运行测试
yarn test

# 生成覆盖率报告
yarn test --coverage
```

## 开发约束和规范

### TypeScript 配置

-   **严格模式**: 启用所有严格类型检查
-   **路径映射**: 支持@src 等别名
-   **类型检查**: 编译时和运行时类型安全

### 代码规范

-   **命名约定**: PascalCase 组件，camelCase 函数
-   **文件组织**: 按功能模块组织代码结构
-   **导入规范**: 绝对路径导入，统一的导入顺序

### 性能要求

-   **首屏加载**: 控制在合理范围内
-   **交互响应**: 用户操作响应时间<200ms
-   **内存使用**: 避免内存泄漏和过度使用

## 依赖管理

### 版本锁定

-   **package-lock.json**: 锁定依赖版本
-   **resolutions**: 解决依赖冲突
-   **安全审计**: 定期检查安全漏洞

### 内部依赖

-   **@mfe/\***: 微前端相关工具包
-   **@roo/\***: 内部 UI 组件库
-   **@ai/\***: AI 相关工具包

### 外部依赖管理

-   **定期更新**: 保持依赖版本相对新
-   **兼容性检查**: 确保升级不破坏现有功能
-   **安全性**: 避免使用有安全漏洞的包

## 调试和监控

### 开发调试

-   **React DevTools**: React 组件调试
-   **Redux DevTools**: 状态管理调试（如果使用）
-   **网络调试**: 浏览器开发者工具

### 错误监控

-   **错误边界**: React 错误捕获
-   **日志系统**: 结构化日志记录
-   **性能监控**: 页面性能指标收集

### 部署监控

-   **健康检查**: 应用可用性监控
-   **性能指标**: 加载时间和交互响应监控
-   **错误追踪**: 生产环境错误收集和分析
