import React from 'react';
import { RightOutlined } from '@ant-design/icons';

// 单个商家诊断任务项
export interface PoiDiagnosisItem {
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: string;
    /** 操作类型 */
    operationType: string;
    /** 内容 */
    content: string;
}

// 任务组（外呼任务或诊断任务）
export interface JobItem {
    /** 任务类型 PoiDiagnosis/AiCall */
    type: string;
    /** 任务状态 */
    status: string;
    /** 子任务列表，当前仅商家诊断任务有值 */
    itemList: PoiDiagnosisItem[];
    /** 创建时间 */
    createTime: number;
    /** 任务id */
    jobId: string;
    /** 完成时间 */
    completeTime: number;
    /** 任务名称 */
    jobName: string;
    /** 商家个数 */
    poiNum: number;
    /** agent名称，type=AiCall时有值 */
    agentName: string;
}

// 兼容旧版本的TaskItem接口，用于商家诊断子项
export interface TaskItem extends PoiDiagnosisItem {
    /** 任务类型 */
    type: string;
    /** 创建时间 */
    createTime: string;
}

interface TaskListItemProps {
    item: TaskItem;
    onViewResult: (item: TaskItem) => void;
}

// 外呼任务组件Props
interface AiCallJobProps {
    job: JobItem;
    onViewResult?: (job: JobItem) => void;
}

// 商家诊断任务组Props
interface PoiDiagnosisJobProps {
    job: JobItem;
    onViewResult: (item: TaskItem) => void;
}

// AI外呼任务组件
const AiCallJobItem: React.FC<AiCallJobProps> = ({ job, onViewResult }) => {
    const getStatusDisplay = () => {
        switch (job.status) {
            case 'init':
                return { text: '进行中', className: 'status-running' };
            case 'success':
                return { text: '已完成', className: 'status-success' };
            case 'fail':
                return { text: '已失败', className: 'status-fail' };
            default:
                return { text: '未知', className: 'status-unknown' };
        }
    };

    const statusInfo = getStatusDisplay();

    const handleClick = () => {
        if (onViewResult && (job.status === 'success' || job.status === 'fail')) {
            onViewResult(job);
        }
    };

    return (
        <div className="job-item-container">
            <div className="job-item-content ai-call-job">
                <div className="job-item-header">
                    <div className="job-item-title">外呼任务</div>
                    <div className={`job-item-status ${statusInfo.className}`}>
                        {job.status === 'init' && (
                            <svg width="12" height="12" viewBox="0 0 12 12" className="status-icon-running">
                                <path d="M6 2 L8 6 L12 8 L8 10 L6 14 L4 10 L0 8 L4 6 Z" fill="currentColor" />
                            </svg>
                        )}
                        <span>{statusInfo.text}</span>
                    </div>
                </div>
                <div className="job-item-details">
                    <div className="job-detail-line">
                        <span className="detail-label">任务名称：</span>
                        <span className="detail-value">{job.jobName}</span>
                    </div>
                    <div className="job-detail-line">
                        <span className="detail-label">外呼商家：</span>
                        <span className="detail-value">{job.poiNum}家</span>
                    </div>
                    <div className="job-detail-line">
                        <span className="detail-label">外呼agent：</span>
                        <span className="detail-value">{job.agentName}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

// 商家诊断任务组件
const PoiDiagnosisJobItem: React.FC<PoiDiagnosisJobProps> = ({ job, onViewResult }) => {
    return (
        <div className="job-item-container">
            <div className="job-item-content poi-diagnosis-job">
                <div className="job-item-header">
                    <div className="job-item-title">商家诊断</div>
                </div>
                <div className="poi-diagnosis-list">
                    {job.itemList?.map((item, index) => (
                        <TaskListItem
                            key={`${job.jobId}-${index}`}
                            item={{ ...item, type: job.type, createTime: new Date(job.createTime).toISOString() }}
                            onViewResult={onViewResult}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    // 处理点击操作
    const handleClick = () => {
        onViewResult(item);
    };

    // 判断是否可以点击
    const isClickable = item.status === 'success' || item.status === 'fail';

    const getStatusDisplay = () => {
        switch (item.status) {
            case 'init':
                return { text: '进行中', className: 'status-running' };
            case 'success':
                return { text: '已完成', className: 'status-success' };
            case 'fail':
                return { text: '已失败', className: 'status-fail' };
            default:
                return { text: '', className: '' };
        }
    };

    const statusInfo = getStatusDisplay();

    return (
        <div
            className={`poi-diagnosis-item ${isClickable ? 'clickable' : ''} ${
                item.status === 'init' ? 'disabled' : ''
            }`}
            onClick={isClickable ? handleClick : undefined}
        >
            {/* 商家图片容器 */}
            <div className="poi-diagnosis-image-container">
                <div className="poi-diagnosis-image">
                    <img src={item.poiAvator} alt="商家图片" />
                </div>
            </div>

            {/* 商家信息 */}
            <div className="poi-diagnosis-content">
                <div className="poi-diagnosis-info">
                    <div className="poi-diagnosis-name">{item.poiName}</div>
                    <div className="poi-diagnosis-id">ID：{item.poiId}</div>
                </div>
                <div className={`poi-diagnosis-status ${statusInfo.className}`}>
                    {statusInfo.text}
                    {isClickable && <RightOutlined className="poi-diagnosis-arrow" />}
                </div>
            </div>
        </div>
    );
};

export default TaskListItem;
export { AiCallJobItem, PoiDiagnosisJobItem };
