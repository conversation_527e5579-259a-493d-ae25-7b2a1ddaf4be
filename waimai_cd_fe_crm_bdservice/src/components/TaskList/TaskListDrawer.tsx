import React, { useEffect, useState } from 'react';
import { Drawer, Empty, Tabs, Switch, Typography } from 'antd';
import { useRequest } from 'ahooks';
import { JobItem, TaskItem, AiCallJobItem, PoiDiagnosisJobItem } from './TaskListItem';
import dayjs from 'dayjs';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import mockJobListData, { mockScenarios } from './mockData';

type TabKey = 'all' | 'aiCall' | 'poiDiagnosis';

interface TaskListDrawerProps {
    open: boolean;
    onClose: () => void;
    onSendMessage: (content: string) => void;
}

interface TaskListResponse {
    jobList: JobItem[];
}

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({ open, onClose, onSendMessage }) => {
    const [activeTab, setActiveTab] = useState<TabKey>('all');
    const [useMockData, setUseMockData] = useState(false); // 默认使用mock数据进行测试
    const [mockScenario, setMockScenario] = useState<keyof typeof mockScenarios>('empty');
    const callerRequest = useCallerRequest();

    // 获取任务列表
    const {
        data: taskData,
        run: refreshTasks,
        loading,
    } = useRequest(
        async () => {
            if (useMockData) {
                // 使用mock数据
                console.log('🧪 使用Mock数据测试, 场景:', mockScenario);
                return new Promise(resolve => {
                    setTimeout(() => {
                        const mockData = mockScenario === 'empty' ? mockJobListData : mockScenarios[mockScenario];
                        if (mockData.code === 0) {
                            resolve(mockData.data);
                        } else {
                            resolve(null);
                        }
                    }, 500); // 模拟网络延迟
                });
            } else {
                // 使用真实API
                const res = await callerRequest.get('/bee/v2/bdaiassistant/job/getList', {});
                if (res.code === 0) {
                    return res.data;
                }
                return null;
            }
        },
        {
            manual: true,
            onError: error => {
                console.error('获取任务列表失败:', error);
            },
        },
    );

    // 当抽屉打开时获取任务列表
    useEffect(() => {
        if (open) {
            refreshTasks();
        }
    }, [open, refreshTasks]);

    // 根据tab筛选任务
    const getFilteredJobs = (): JobItem[] => {
        if (!taskData?.jobList) return [];

        let filteredJobs = taskData.jobList;

        switch (activeTab) {
            case 'aiCall':
                filteredJobs = taskData.jobList.filter(job => job.type === 'AiCall');
                break;
            case 'poiDiagnosis':
                filteredJobs = taskData.jobList.filter(job => job.type === 'PoiDiagnosis');
                break;
            case 'all':
            default:
                filteredJobs = taskData.jobList;
                break;
        }

        // 按创建时间倒序排序
        return filteredJobs.sort((a, b) => b.createTime - a.createTime);
    };

    // 获取统计数据
    const getStats = () => {
        if (!taskData?.jobList) {
            return { running: 0, success: 0, fail: 0 };
        }

        const jobs = getFilteredJobs();
        let running = 0,
            success = 0,
            fail = 0;

        jobs.forEach(job => {
            if (job.type === 'PoiDiagnosis') {
                // 商家诊断任务统计子项状态
                job.itemList?.forEach(item => {
                    switch (item.status) {
                        case 'init':
                            running++;
                            break;
                        case 'success':
                            success++;
                            break;
                        case 'fail':
                            fail++;
                            break;
                    }
                });
            } else {
                // 其他任务直接统计
                switch (job.status) {
                    case 'init':
                        running++;
                        break;
                    case 'success':
                        success++;
                        break;
                    case 'fail':
                        fail++;
                        break;
                }
            }
        });

        return { running, success, fail };
    };

    const handleViewTaskResult = (item: TaskItem) => {
        // 构建查询消息
        const queryMessage = item.content;

        // 发送消息到聊天界面
        onSendMessage(queryMessage);

        // 关闭抽屉
        onClose();
    };

    const handleViewJobResult = (job: JobItem) => {
        // 处理外呼任务结果查看
        console.log('查看任务结果:', job);
        // 这里可以实现具体的任务结果查看逻辑
    };

    const formatTime = (timestamp: number): string => {
        const date = dayjs(timestamp);
        const today = dayjs().startOf('day');

        if (date.isSame(today, 'day')) {
            return `今日 ${date.format('HH:mm')}`;
        } else {
            return `${date.format('M月D日 HH:mm')}`;
        }
    };

    const stats = getStats();

    return (
        <Drawer
            title={
                <div className="task-list-drawer-header">
                    <div className="task-list-drawer-title-container">
                        <div className="task-list-drawer-title-text">今日任务</div>
                        <div style={{ display: 'flex', cursor: 'pointer', alignItems: 'center' }} onClick={onClose}>
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                style={{ zoom: 0.85 }}
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M5 4V20"
                                    stroke="#999999"
                                    stroke-width="1.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                                <path
                                    d="M28 12H8 M14 6L8 12L14 18"
                                    stroke="#999999"
                                    stroke-width="1.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </div>
                    </div>
                </div>
            }
            open={open}
            onClose={onClose}
            width={328}
            destroyOnClose
            closeIcon={false}
            styles={{
                body: {
                    padding: 0,
                },
            }}
            placement="left"
            className="task-list-drawer"
        >
            <div className="task-list-drawer-content">
                {/* Mock数据测试控制面板 */}
                {useMockData && (
                    <div className="task-list-drawer-mock-panel">
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                            <Switch size="small" checked={useMockData} onChange={setUseMockData} />
                            <Typography.Text style={{ fontSize: 12, color: '#666' }}>使用Mock数据测试</Typography.Text>
                        </div>
                        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
                            {Object.keys(mockScenarios).map(scenario => (
                                <button
                                    key={scenario}
                                    onClick={() => {
                                        setMockScenario(scenario as keyof typeof mockScenarios);
                                        refreshTasks();
                                    }}
                                    style={{
                                        padding: '2px 8px',
                                        fontSize: 10,
                                        border: '1px solid #d9d9d9',
                                        borderRadius: 4,
                                        background: mockScenario === scenario ? '#1890ff' : '#fff',
                                        color: mockScenario === scenario ? '#fff' : '#666',
                                        cursor: 'pointer',
                                    }}
                                >
                                    {scenario}
                                </button>
                            ))}
                            <button
                                onClick={() => {
                                    setMockScenario('empty');
                                    refreshTasks();
                                }}
                                style={{
                                    padding: '2px 8px',
                                    fontSize: 10,
                                    border: '1px solid #d9d9d9',
                                    borderRadius: 4,
                                    background: mockScenario === 'empty' ? '#1890ff' : '#fff',
                                    color: mockScenario === 'empty' ? '#fff' : '#666',
                                    cursor: 'pointer',
                                }}
                            >
                                完整数据
                            </button>
                        </div>
                    </div>
                )}

                {/* 标签页区域 */}
                <div className="task-list-drawer-tabs-container">
                    <Tabs
                        activeKey={activeTab}
                        onChange={key => setActiveTab(key as TabKey)}
                        items={[
                            {
                                key: 'all',
                                label: '全部',
                            },
                            {
                                key: 'poiDiagnosis',
                                label: '商家诊断',
                            },
                            {
                                key: 'aiCall',
                                label: 'AI外呼',
                            },
                        ]}
                        size="small"
                        className="task-list-tabs"
                    />
                </div>

                {/* 任务列表区域 */}
                <div className="task-list-drawer-list-container">
                    {getFilteredJobs().map((job, jobIndex) => (
                        <div key={`${job.jobId}-${jobIndex}`} className="task-list-timeline-item">
                            <div className="timeline-dot" />
                            <div className="timeline-content">
                                <div className="timeline-time">{formatTime(job.createTime)}</div>
                                <div className="timeline-card">
                                    {job.type === 'AiCall' ? (
                                        <AiCallJobItem job={job} onViewResult={handleViewJobResult} />
                                    ) : job.type === 'PoiDiagnosis' ? (
                                        <PoiDiagnosisJobItem job={job} onViewResult={handleViewTaskResult} />
                                    ) : null}
                                </div>
                            </div>
                        </div>
                    ))}

                    {getFilteredJobs().length === 0 && <Empty description="暂无任务" style={{ marginTop: 60 }} />}
                </div>
            </div>
        </Drawer>
    );
};

export default TaskListDrawer;
