// 任务列表Mock数据
// 用于测试新的UI 2.0样式

export const mockJobListData = {
    code: 0,
    msg: 'success',
    data: {
        jobList: [
            // 今日 12:34 - 外呼任务（进行中）
            {
                type: 'AiCall',
                status: 'init',
                itemList: [],
                createTime: Date.now() - 2 * 60 * 60 * 1000, // 2小时前
                jobId: 'aicall_001',
                completeTime: 0,
                jobName: '店铺分智能AI外呼-7896',
                poiNum: 12,
                agentName: '店铺分智能AI外呼(lilipifei51)',
            },
            // 今日 12:34 - 商家诊断任务
            {
                type: 'PoiDiagnosis',
                status: 'mixed', // 混合状态，子项有不同状态
                itemList: [
                    {
                        poiId: 902001202,
                        poiName: '兰州牛肉拉面·清真食品专营店',
                        poiAvator: 'https://p0.meituan.net/merchant/7e9b2de8a0b1c9c7f1d2e3f4a5b6c7d8.jpg',
                        status: 'success',
                        abilityType: 'diagnosis',
                        operationType: 'auto',
                        content: '商家基础信息诊断完成，发现3个可优化项',
                    },
                    {
                        poiId: 902001203,
                        poiName: '林富强水饺·炸鸡·炸酱面·东北菜',
                        poiAvator: 'https://p0.meituan.net/merchant/8f0c3de9a1b2c0d8f2e3f5a6b7c8d9e0.jpg',
                        status: 'init',
                        abilityType: 'diagnosis',
                        operationType: 'auto',
                        content: '正在进行商家诊断分析...',
                    },
                    {
                        poiId: 902001204,
                        poiName: '兰州牛肉拉面·清真食品专营店',
                        poiAvator: 'https://p0.meituan.net/merchant/9g1d4df0b2c3d1e9f3f4a7b8c9d0e1f2.jpg',
                        status: 'fail',
                        abilityType: 'diagnosis',
                        operationType: 'auto',
                        content: '商家诊断失败，请稍后重试',
                    },
                ],
                createTime: Date.now() - 2 * 60 * 60 * 1000, // 2小时前
                jobId: 'diagnosis_001',
                completeTime: 0,
                jobName: '商家诊断任务',
                poiNum: 3,
                agentName: '',
            },
            // 7月22日 12:34 - 外呼任务（已完成）
            {
                type: 'AiCall',
                status: 'success',
                itemList: [],
                createTime: Date.now() - 24 * 60 * 60 * 1000 - 2 * 60 * 60 * 1000, // 昨天
                jobId: 'aicall_002',
                completeTime: Date.now() - 24 * 60 * 60 * 1000 - 1 * 60 * 60 * 1000,
                jobName: '店铺分智能AI外呼-7896',
                poiNum: 12,
                agentName: '店铺分智能AI外呼(lilipifei51)',
            },
            // 7月22日 10:15 - 商家诊断任务（部分完成）
            {
                type: 'PoiDiagnosis',
                status: 'mixed',
                itemList: [
                    {
                        poiId: 902001205,
                        poiName: '麦当劳(万达广场店)',
                        poiAvator: 'https://p0.meituan.net/merchant/a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7.jpg',
                        status: 'success',
                        abilityType: 'diagnosis',
                        operationType: 'manual',
                        content: '门店运营数据分析完成',
                    },
                    {
                        poiId: 902001206,
                        poiName: '星巴克(CBD店)',
                        poiAvator: 'https://p0.meituan.net/merchant/b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8.jpg',
                        status: 'success',
                        abilityType: 'diagnosis',
                        operationType: 'manual',
                        content: '营销活动效果分析完成',
                    },
                    {
                        poiId: 902001207,
                        poiName: '肯德基(中心店)',
                        poiAvator: 'https://p0.meituan.net/merchant/c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9.jpg',
                        status: 'fail',
                        abilityType: 'diagnosis',
                        operationType: 'auto',
                        content: '数据获取失败，请检查商家权限',
                    },
                ],
                createTime: Date.now() - 24 * 60 * 60 * 1000 - 4 * 60 * 60 * 1000, // 昨天
                jobId: 'diagnosis_002',
                completeTime: Date.now() - 24 * 60 * 60 * 1000 - 3 * 60 * 60 * 1000,
                jobName: '商家诊断任务',
                poiNum: 3,
                agentName: '',
            },
            // 7月21日 - 外呼任务（失败）
            {
                type: 'AiCall',
                status: 'fail',
                itemList: [],
                createTime: Date.now() - 2 * 24 * 60 * 60 * 1000, // 前天
                jobId: 'aicall_003',
                completeTime: Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000,
                jobName: '店铺分智能AI外呼-7895',
                poiNum: 8,
                agentName: '店铺分智能AI外呼(lilipifei51)',
            },
            // 更早的任务 - 商家诊断（全部成功）
            {
                type: 'PoiDiagnosis',
                status: 'success',
                itemList: [
                    {
                        poiId: 902001208,
                        poiName: '海底捞火锅(万象城店)',
                        poiAvator: 'https://p0.meituan.net/merchant/d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9g0.jpg',
                        status: 'success',
                        abilityType: 'comprehensive',
                        operationType: 'auto',
                        content: '综合诊断完成，门店表现优秀',
                    },
                    {
                        poiId: 902001209,
                        poiName: '喜茶(太古里店)',
                        poiAvator: 'https://p0.meituan.net/merchant/e6f7a8b9c0d1e2f3a4b5c6d7e8f9g0h1.jpg',
                        status: 'success',
                        abilityType: 'marketing',
                        operationType: 'manual',
                        content: '品牌营销策略优化建议已生成',
                    },
                ],
                createTime: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3天前
                jobId: 'diagnosis_003',
                completeTime: Date.now() - 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000,
                jobName: '重点商家诊断',
                poiNum: 2,
                agentName: '',
            },
            // 绩效日报任务（未来扩展用）
            {
                type: 'Performance',
                status: 'success',
                itemList: [],
                createTime: Date.now() - 60 * 60 * 1000, // 1小时前
                jobId: 'performance_001',
                completeTime: Date.now() - 30 * 60 * 1000,
                jobName: '7月份商家绩效日报',
                poiNum: 0,
                agentName: '',
            },
        ],
    },
};

// 导出不同场景的mock数据
export const mockScenarios = {
    // 空数据场景
    empty: {
        code: 0,
        msg: 'success',
        data: {
            jobList: [],
        },
    },

    // 只有外呼任务
    onlyAiCall: {
        code: 0,
        msg: 'success',
        data: {
            jobList: mockJobListData.data.jobList.filter(job => job.type === 'AiCall'),
        },
    },

    // 只有商家诊断
    onlyDiagnosis: {
        code: 0,
        msg: 'success',
        data: {
            jobList: mockJobListData.data.jobList.filter(job => job.type === 'PoiDiagnosis'),
        },
    },

    // 错误场景
    error: {
        code: -1,
        msg: '网络请求失败',
        data: null,
    },
};

export default mockJobListData;
