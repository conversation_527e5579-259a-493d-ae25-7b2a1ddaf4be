# 任务列表 UI 2.0 测试指南

## 📋 测试概述

本文档介绍如何使用 Mock 数据测试新的任务列表 UI 2.0 功能。

## 🧪 Mock 数据测试模式

### 启用 Mock 模式

1. 打开任务列表抽屉
2. 在顶部可以看到"使用 Mock 数据测试"开关
3. 默认开启 Mock 模式，显示完整测试数据

### 测试场景切换

Mock 模式下提供以下测试场景：

#### 1. **完整数据** (默认场景)

-   包含多种任务类型：AI 外呼、商家诊断、绩效日报
-   包含不同状态：进行中、成功、失败
-   包含不同时间：今日、昨天、前天等
-   测试所有 UI 功能和交互

#### 2. **empty** - 空数据场景

-   测试空状态显示
-   验证"暂无任务"提示

#### 3. **onlyAiCall** - 仅外呼任务

-   只显示 AI 外呼任务
-   测试外呼任务的专用 UI 组件
-   验证标签筛选功能

#### 4. **onlyDiagnosis** - 仅商家诊断

-   只显示商家诊断任务
-   测试商家诊断的列表展示
-   验证商家信息显示

#### 5. **error** - 错误场景

-   模拟 API 请求失败
-   测试错误处理逻辑

## 🎯 测试重点功能

### 1. 标签页筛选

-   [ ] "全部"标签显示所有任务
-   [ ] "AI 外呼"标签只显示外呼任务
-   [ ] "绩效日报"标签显示绩效任务

### 2. 时间线布局

-   [ ] 时间显示格式：今日显示"今日 12:34"
-   [ ] 历史任务显示"7 月 22 日 12:34"格式
-   [ ] 时间线连接线和圆点显示正常
-   [ ] 任务按时间倒序排列

### 3. 差异化任务展示

#### AI 外呼任务

-   [ ] 显示任务名称
-   [ ] 显示"外呼商家：X 家"
-   [ ] 显示"外呼 agent：名称"
-   [ ] 状态标签正确显示和着色
-   [ ] 进行中状态有动画效果

#### 商家诊断任务

-   [ ] 显示商家列表
-   [ ] 商家头像正确显示
-   [ ] 商家名称和 ID 显示
-   [ ] 状态标签位置和样式正确
-   [ ] 点击交互正常(已完成/失败状态)

### 4. 状态可视化

-   [ ] 进行中：蓝色背景，带动画
-   [ ] 已完成：绿色背景
-   [ ] 已失败：红色背景
-   [ ] 状态统计数据正确

### 5. 交互功能

-   [ ] 已完成任务点击可查看详情
-   [ ] 已失败任务点击可查看详情
-   [ ] 进行中任务不可点击
-   [ ] 鼠标悬停效果正常

## 📊 Mock 数据结构

### 完整数据包含：

#### 今日任务

-   1 个进行中的外呼任务（12 家商家）
-   1 个混合状态的商家诊断任务（3 个商家：成功 1 个、进行中 1 个、失败 1 个）

#### 历史任务

-   昨天：1 个成功的外呼任务 + 1 个混合状态的商家诊断
-   前天：1 个失败的外呼任务
-   3 天前：1 个全部成功的商家诊断任务
-   绩效日报任务（用于未来扩展）

## 🔄 切换到真实 API

当需要测试真实 API 时：

1. 关闭"使用 Mock 数据测试"开关
2. 系统将调用真实的`/bee/v2/bdaiassistant/jobList/list`接口
3. 如果真实 API 返回数据，会按新的 UI 格式展示

## 🐛 测试注意事项

1. **控制台日志**：Mock 模式会在控制台输出测试场景信息
2. **网络延迟模拟**：Mock 数据有 500ms 延迟，模拟真实网络环境
3. **数据刷新**：切换测试场景时会自动刷新数据
4. **样式验证**：重点关注时间线布局、状态标签、动画效果
5. **响应式测试**：在不同屏幕尺寸下测试布局适配

## 📝 测试检查清单

### UI 展示测试

-   [ ] 标签页切换正常
-   [ ] 时间线布局正确
-   [ ] 任务卡片样式完整
-   [ ] 状态标签颜色正确
-   [ ] 动画效果流畅

### 交互测试

-   [ ] 可点击任务响应正常
-   [ ] 不可点击任务无响应
-   [ ] 场景切换功能正常
-   [ ] Mock/真实 API 切换正常

### 边界测试

-   [ ] 空数据状态正常
-   [ ] 错误状态处理正常
-   [ ] 长文本显示正常
-   [ ] 大量数据渲染正常

## 🎨 样式验证重点

1. **时间线视觉**：圆点、连接线、时间文字对齐
2. **状态标签**：圆角、颜色、动画效果
3. **商家头像**：尺寸、圆角、占位图
4. **文字排版**：字体、大小、颜色、行高
5. **间距布局**：组件间距、内边距、对齐方式

完成以上测试后，任务列表 UI 2.0 就可以投入生产使用了！
