import { useEffect, useState } from 'react';

// convert query to state
export const useQuery = <T extends string>(keys: T[] = []) => {
    const [query, setQuery] = useState<Record<T, string | undefined>>({} as Record<T, string | undefined>);
    const [loaded, setLoaded] = useState(false);

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);

        const all = [...searchParams].reduce((memo, [k, v]) => ({ ...memo, [k]: v }), {});

        setLoaded(true);
        if (!keys.length) {
            setQuery(all as Record<T, string | undefined>);
            return;
        }

        const next = keys.reduce((memo, key) => {
            // 如果参数存在但没有值（如?devMock），searchParams.get()返回空字符串
            // 如果参数不存在，searchParams.get()返回null
            const value = searchParams.get(key);
            memo[key] = value !== null ? value || '' : undefined;
            return memo;
        }, {} as Record<T, string | undefined>);

        setQuery(next);
    }, []);

    return { query, loaded };
};
