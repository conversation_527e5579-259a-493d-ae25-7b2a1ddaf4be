import React, { useState, Suspense } from 'react';
import { Modal, Form, message, Spin } from 'antd';
import CreateTask from 'crm-ai/CreateTaskFederation';

interface AICallModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (params: any) => void;
    initialParams?: any;
}

const AICallModal: React.FC<AICallModalProps> = ({ visible, onClose, onSubmit, initialParams = {} }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const params = {
                ...values,
                ...initialParams,
            };

            await onSubmit(params);
            message.success('外呼任务创建成功');
            form.resetFields();
            onClose();
        } catch (error) {
            console.error('创建外呼任务失败:', error);
            message.error('创建外呼任务失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateSuccess = (taskId?: string | number) => {
        console.log('任务创建成功，taskId:', taskId);
        form.resetFields();
        onClose();
    };

    const handleCancel = () => {
        form.resetFields();
        onClose();
    };

    return (
        <Modal title="创建外呼任务" open={visible} onCancel={handleCancel} width={600} footer={null}>
            <Suspense
                fallback={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: 200,
                        }}
                    >
                        <Spin size="large" tip="正在加载外呼任务组件..." />
                    </div>
                }
            >
                <CreateTask
                    {...initialParams} // 预填充字段，具体见下方
                    onCreateSuccess={handleCreateSuccess}
                    onCancel={handleCancel}
                />
            </Suspense>
        </Modal>
    );
};

export default AICallModal;
