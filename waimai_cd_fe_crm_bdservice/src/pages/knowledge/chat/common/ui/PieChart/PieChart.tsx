import React from 'react';
import { Card } from 'antd';
import ReactECharts from 'echarts-for-react';
import './PieChart.scss';

interface PieChartData {
    label: string;
    value: number;
    color?: string; // color is now optional, we'll use a predefined palette
}

interface PieChartProps {
    data: PieChartData[];
    title?: string;
}

const PieChart: React.FC<PieChartProps> = ({ data, title }) => {
    const getOption = () => {
        const legendData = data.map(item => item.label);
        const seriesData = data.map(item => ({
            name: item.label,
            value: item.value,
        }));

        const total = data.reduce((sum, item) => sum + item.value, 0);

        return {
            tooltip: {
                trigger: 'item',
                formatter: (params: any) => {
                    const percent = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
                    return `${params.name}: ${params.value} (${percent}%)`;
                },
            },
            legend: {
                orient: 'horizontal',
                bottom: '0',
                left: 'center',
                width: 220,
                data: legendData,
                itemGap: 20,
                icon: 'rect',
                itemWidth: 7,
                itemHeight: 7,
                formatter: (name: string) => {
                    const item = data.find(d => d.label === name);
                    if (!item) return name;
                    const percent = total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0';
                    return `{name|${name}} {percent|${percent}%}`;
                },
                textStyle: {
                    rich: {
                        name: {
                            color: '#8C8C8C',
                            fontSize: 12,
                        },
                        percent: {
                            color: '#000',
                            fontSize: 12,
                        },
                    },
                },
            },
            series: [
                {
                    name: 'Access From',
                    type: 'pie',
                    radius: ['30%', '60%'],
                    center: ['50%', '35%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 2,
                    },
                    label: {
                        show: false,
                        position: 'center',
                    },
                    emphasis: {
                        label: {
                            show: false,
                        },
                    },
                    labelLine: {
                        show: false,
                    },
                    data: seriesData,
                },
            ],
            color: ['#3A86FF', '#3D9970', '#FF851B', '#FFDC00'], // Matching UI colors
        };
    };

    const legendRows = Math.ceil(data.length / 2);
    const chartHeight = 230 + legendRows * 20;

    return (
        <Card className="pie-chart-card">
            <ReactECharts option={getOption()} style={{ height: `${chartHeight}px`, width: '100%' }} />
        </Card>
    );
};

export default PieChart;
