.ai-call-record-item {
    background: #F5F6FA;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border-color: #e6f7ff;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .item-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 16px;
    }

    .info-section {
        flex: 1;
        min-width: 0; // 防止内容溢出

        .title-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .task-name {
                font-size: 16px;
                font-weight: 500;
                color: #1f2329;
                line-height: 1.4;
                flex: 1;
                margin-right: 12px;
            }

            .status-running {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 2px 8px;
                background: #e6f7ff;
                color: #1890ff;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
                flex-shrink: 0;

                .status-icon {
                    font-size: 10px;
                    animation: rotate 2s linear infinite;
                }
            }
        }

        .descriptions-section {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .description-item {
                display: flex;
                align-items: center;
                font-size: 13px;
                line-height: 1.4;

                .label {
                    color: #86909c;
                    margin-right: 6px;
                    white-space: nowrap;
                    min-width: fit-content;
                }

                .value {
                    color: #1f2329;
                    font-weight: 500;
                    word-break: break-all;
                }
            }
        }
    }

    .action-section {
        margin: auto;

        .view-results-btn {
            background: #FFDD00;
            border: 1px solid #FFDD00;
            color: #1f2329;
            height: 32px;
            padding: 0 16px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            border-radius: 14px!important;

            &:hover,
            &:focus {
                background: #ffda4f;
                border-color: #ffda4f;
                color: #1f2329;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(255, 195, 0, 0.3);
            }
        }
    }
}

// 展开/收起按钮样式
.ant-btn-link {
    color: #86909c;
    font-size: 14px;
    height: auto;
    line-height: 1.4;
    display: inline-flex;
    align-items: center;

    &:hover {
        color: #1f2329;
    }

    &:focus {
        color: #86909c;
    }

    .anticon {
        font-size: 12px;
    }
}

// 动画效果
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .ai-call-record-item {
        padding: 12px;
        border-radius: 8px;

        .item-content {
            flex-direction: column;
            gap: 12px;
        }

        .info-section {
            .title-line {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .task-name {
                    margin-right: 0;
                }

                .status-running {
                    align-self: flex-start;
                }
            }

            .descriptions-section {
                gap: 4px;

                .description-item {
                    .label {
                        margin-right: 4px;
                    }
                }
            }
        }

        .action-section {
            align-self: center;

            .view-results-btn {
                width: 120px;
            }
        }
    }
}
