import React, { useState, useContext } from 'react';
import { Button } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import MessageContext from '@src/pages/knowledge/chat/common/ui/message/messageContext';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import './AICallRecord.scss';

interface AICallRecordItem {
    jobName: string;
    status: 'init' | 'running' | 'success' | 'fail';
    descriptions: {
        label: string;
        value: string;
    }[];
    button: {
        text: string;
        url?: string;
        question?: string;
        action?: 'submitQuestion' | 'openAICallModal';
        AICallParams?: any;
        color?: string;
        type?: 'primary' | 'normal';
    };
    createTime?: number;
    completeTime?: number;
    progress?: number;
}

interface AICallRecordProps {
    data: {
        content: AICallRecordItem[];
        extendButtonName: string;
        showNum: number;
    };
}

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const { content, extendButtonName, showNum } = data;
    const [expanded, setExpanded] = useState(false);

    // 获取必要的依赖
    const sendMessage = useSendMessage();
    const { serverId, history } = useContext(MessageContext);
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();

    // 根据 showNum 和 expanded 状态决定显示的项目
    const displayItems = expanded || !showNum ? content : content.slice(0, showNum);
    const showExpandButton = extendButtonName && content.length > showNum;

    const handleButtonClick = (button: AICallRecordItem['button']) => {
        if (button.action === 'submitQuestion' && button.question) {
            // 使用 useSendMessage 发送消息
            sendMessage(button.question, {});
        } else if (button.action === 'openAICallModal') {
            console.log('Open AI Call Modal:', button.AICallParams);
        } else if (button.url) {
            // 使用 openLink 处理 URL 跳转
            openLink(button.url, serverId, sessionId, bizInfo?.uid, history);
        }
    };

    const getStatusDisplay = (status: string) => {
        const isRunning = status === 'running';
        return { isRunning };
    };

    return (
        <div className="ai-call-record-container">
            {displayItems.map((item, index) => {
                const { isRunning } = getStatusDisplay(item.status);

                return (
                    <div key={index} className="ai-call-record-item">
                        <div className="item-content">
                            <div className="info-section">
                                <div className="title-line">
                                    <span className="task-name">{item.jobName}</span>
                                    {isRunning && (
                                        <span className="status-running">
                                            <span className="status-icon">✦</span> 进行中
                                        </span>
                                    )}
                                </div>
                                <div className="descriptions-section">
                                    {item.descriptions.map((desc, descIndex) => (
                                        <div key={descIndex} className="description-item">
                                            <span className="label">{desc.label}:</span>
                                            <span className="value">{desc.value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            {!isRunning && (
                                <div className="action-section">
                                    <Button className="view-results-btn" onClick={() => handleButtonClick(item.button)}>
                                        {item.button.text}
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                );
            })}

            {showExpandButton && (
                <div style={{ textAlign: 'center', marginTop: '8px' }}>
                    <Button
                        type="link"
                        onClick={() => setExpanded(!expanded)}
                        style={{ padding: '4px 8px', fontSize: '14px' }}
                    >
                        共{content.length} {expanded ? '收起' : '展开'}
                        {expanded ? (
                            <UpOutlined style={{ marginLeft: '4px' }} />
                        ) : (
                            <DownOutlined style={{ marginLeft: '4px' }} />
                        )}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default AICallRecord;
