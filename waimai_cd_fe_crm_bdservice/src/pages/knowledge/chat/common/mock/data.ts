// Mock数据的元数据配置
export const mockDataMeta = {
    imgData: { label: '图文消息', description: '包含文本和图片的混合消息' },
    formDataWithRegex: { label: '表单数据', description: '带有正则验证的表单数据' },
    optionData: { label: '选项数据', description: '包含选择项的数据' },
    actionCardSingleButton: { label: 'ActionCard单按钮', description: '单个操作按钮的卡片' },
    actionCardMultipleButtons: { label: 'ActionCard多按钮', description: '多个操作按钮的卡片' },
    pieChartData: { label: '饼图数据', description: '用于展示饼图的数据' },
    aiCallRecordRunning: { label: '外呼记录-执行中', description: '正在执行中的外呼任务记录' },
    aiCallRecordCompleted: { label: '外呼记录-已完成', description: '已完成的外呼任务记录' },
    aiCallRecordFailed: { label: '外呼记录-执行失败', description: '执行失败的外呼任务记录' },
    aiCallCompleteFlow: { label: '外呼完整流程', description: '包含完整外呼流程的综合数据' },
    pieChartSmall: { label: '小饼图数据', description: '小数据量的饼图示例' },
} as const;

export const mockData = {
    // 图文消息
    imgData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135622',
            msgId: '5135632',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"text","insert":"若商家未收到下线清退短信链接可让商家在商家端查看站内消息并确认，如图所示：\\n"},{"type":"image","insert":{"image":"https://s3plus.sankuai.com/bdaiassistant-public/image_1749542173900-20250610.png"}},{"type":"image","insert":{"image":"https://km.sankuai.com/api/file/cdn/1429409002/14123602886111?contentType=0&isNewContent=false"}},{"type":"text","insert":"\\n预计7月小蜜会接入下线清退短信链接，敬请期待~"}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
    // 表单数据
    formDataWithRegex: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135622',
            msgId: '5135633',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '商家名称',
                                    type: 'input',
                                    defaultValue: '',
                                    tooltip: '请输入您的商家名称',
                                    labelWrap: false,
                                    regExp: '^.{2,50}$',
                                    message: '商家名称长度应在2-50个字符之间',
                                },
                                {
                                    label: '联系电话',
                                    type: 'input',
                                    defaultValue: '',
                                    tooltip: '请输入您的联系电话，方便我们与您联系',
                                    labelWrap: false,
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号码格式',
                                },
                                {
                                    label: 'BDmis',
                                    type: 'input',
                                    defaultValue: 'jiangao',
                                },
                                {
                                    label: '绩效目标',
                                    type: 'radio',
                                    options: ['100%', '130%', '150%'],
                                    defaultValue: '100%',
                                },
                            ],
                            buttonText: '提交',
                            title: '新签攻克绩效',
                            subTitle: '我们已经了解到您要分析本月新签攻克绩效数据。请补充相关信息。',
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
    // option 选项
    optionData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '96163',
            msgId: '96166',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"text","insert":"请选择需要查询的场景"},{"type":"options","insert":{"options":{"options":[{"abilityType":1,"operationType":2,"content":"外卖入驻（切换客户kp签约）"},{"abilityType":1,"operationType":2,"content":"下线清退确认"},{"abilityType":1,"operationType":2,"content":"拼好饭签约"},{"abilityType":1,"operationType":2,"content":"美食城承诺书签约"},{"abilityType":1,"operationType":2,"content":"重新建店签约"}],"tabs":[{"label":"上线&清退","value":"上线&清退"},{"label":"配送/结算/推广","value":"配送/结算/推广"},{"label":"切换客户","value":"切换客户"}]}}}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1753253839883,
            tags: null,
        },
    },

    // ActionCard 单按钮
    actionCardSingleButton: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135624',
            msgId: '5135634',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '基于您的商家数据分析，我们发现以下问题需要关注：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '串联次新运营绩效诊断',
                            subTitle: '本月新签商家转化率较低，建议优化运营策略提升商家活跃度',
                            backgroundColor: '#f0f8ff',
                            button: {
                                text: '查看详细报告',
                                action: 'submitQuestion',
                                question: '请提供详细的串联次新运营绩效分析报告',
                                type: 'primary',
                                color: '#1890ff',
                            },
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // ActionCard 多按钮
    actionCardMultipleButtons: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135625',
            msgId: '5135635',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '根据分析结果，为您提供以下操作选项：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '商家运营优化建议',
                            subTitle: '基于数据分析，我们为您准备了多种优化方案',
                            backgroundColor: '#fff7e6',
                            buttonList: [
                                {
                                    text: '查看报告',
                                    action: 'submitQuestion',
                                    question: '请展示详细的运营分析报告',
                                    type: 'primary',
                                },
                                {
                                    text: '创建外呼任务',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家运营优化外呼',
                                        callScript: '您好，我们发现您的店铺运营数据有优化空间，想为您提供一些建议...',
                                        targetCount: 50,
                                    },
                                    type: 'normal',
                                },
                                {
                                    text: '导出数据',
                                    url: 'https://example.com/export-data',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // PieChart 饼图数据
    pieChartData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135626',
            msgId: '5135636',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '外呼任务执行结果统计如下：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '外呼结果统计',
                            data: [
                                {
                                    label: '接通成功',
                                    value: 450,
                                    color: '#6047FA',
                                },
                                {
                                    label: '接通失败',
                                    value: 180,
                                    color: '#FF6B6B',
                                },
                                {
                                    label: '用户拒接',
                                    value: 120,
                                    color: '#FFD93D',
                                },
                                {
                                    label: '号码无效',
                                    value: 50,
                                    color: '#A8A8A8',
                                },
                            ],
                            size: 200,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 执行中
    aiCallRecordRunning: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135627',
            msgId: '5135637',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '您的外呼任务正在执行中，当前进度如下：',
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        { label: '创建时间', value: '2024.01.15 10:30:00' },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000, // 2024-01-15 10:30:00
                                    progress: 65,
                                },
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        { label: '创建时间', value: '2024-01-15 10:30:00' },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000, // 2024-01-15 10:30:00
                                    progress: 65,
                                },
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        { label: '创建时间', value: '2024-01-15 10:30:00' },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000, // 2024-01-15 10:30:00
                                    progress: 65,
                                },
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        { label: '创建时间', value: '2024-01-15 10:30:00' },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000, // 2024-01-15 10:30:00
                                    progress: 65,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 2,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 已完成
    aiCallRecordCompleted: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135628',
            msgId: '5135638',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '外呼任务已完成，执行结果如下：',
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                        { label: '成功接通', value: '380家' },
                                        { label: '失败', value: '120家' },
                                        { label: '创建时间', value: '2024-01-14 09:00:00' },
                                        { label: '完成时间', value: '2024-01-14 18:30:00' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000, // 2024-01-14 09:00:00
                                    completeTime: 1705240200000, // 2024-01-14 18:30:00
                                    progress: 100,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 1,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 执行失败
    aiCallRecordFailed: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135629',
            msgId: '5135639',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '外呼任务执行失败，请查看详情：',
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '商家满意度调研外呼',
                                    status: 'fail',
                                    descriptions: [
                                        { label: '外呼商家', value: '800家' },
                                        { label: '已完成', value: '200家' },
                                        { label: '成功接通', value: '150家' },
                                        { label: '失败原因', value: '系统异常导致任务中断' },
                                        { label: '创建时间', value: '2024-01-13 14:00:00' },
                                        { label: '中断时间', value: '2024-01-13 16:45:00' },
                                    ],
                                    button: {
                                        text: '重新创建',
                                        action: 'openAICallModal',
                                        AICallParams: {
                                            taskName: '商家满意度调研外呼',
                                            callScript: '您好，我们想了解您对我们服务的满意度...',
                                            targetCount: 800,
                                        },
                                        type: 'primary',
                                    },
                                    createTime: 1705122000000, // 2024-01-13 14:00:00
                                    progress: 25,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 1,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // 综合场景：外呼任务完整流程
    aiCallCompleteFlow: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135630',
            msgId: '5135640',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '基于您的商家数据分析，我们为您提供完整的外呼解决方案：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '外呼任务管理中心',
                            subTitle: '一站式外呼任务创建、监控和分析平台',
                            backgroundColor: '#f6ffed',
                            buttonList: [
                                {
                                    text: '创建新任务',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家激活外呼',
                                        callScript: '您好，恭喜您成功入驻美团外卖！我们想为您介绍一些运营技巧...',
                                        targetCount: 100,
                                    },
                                    type: 'primary',
                                },
                                {
                                    text: '查看历史',
                                    action: 'submitQuestion',
                                    question: '请显示我的外呼任务历史记录',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n最近任务执行情况：',
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '本周商家关怀外呼',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '300家' },
                                        { label: '已完成', value: '234家' },
                                        { label: '成功接通', value: '180家' },
                                        { label: '失败', value: '54家' },
                                        { label: '创建时间', value: '2024-01-15 08:00:00' },
                                        { label: '最近更新', value: '2024-01-15 15:30:00' },
                                    ],
                                    button: {
                                        text: '查看进度',
                                        action: 'submitQuestion',
                                        question: '查看本周商家关怀外呼详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705283200000, // 2024-01-15 08:00:00
                                    progress: 78,
                                },
                                {
                                    jobName: '商家激活提醒外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '150家' },
                                        { label: '已完成', value: '150家' },
                                        { label: '成功接通', value: '120家' },
                                        { label: '创建时间', value: '2024-01-14 16:00:00' },
                                        { label: '完成时间', value: '2024-01-15 10:00:00' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看商家激活提醒外呼结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705231200000, // 2024-01-14 16:00:00
                                    completeTime: 1705291200000, // 2024-01-15 10:00:00
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家欢迎外呼',
                                    status: 'init',
                                    descriptions: [
                                        { label: '外呼商家', value: '200家' },
                                        { label: '计划开始', value: '2024-01-16 09:00:00' },
                                        { label: '创建时间', value: '2024-01-15 17:00:00' },
                                    ],
                                    button: {
                                        text: '立即开始',
                                        action: 'submitQuestion',
                                        question: '立即开始新签商家欢迎外呼任务',
                                        type: 'primary',
                                    },
                                    createTime: 1705316400000, // 2024-01-15 17:00:00
                                },
                            ],
                            extendButtonName: '查看更多',
                            showNum: 2,
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n整体外呼效果分析：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '本月外呼结果汇总',
                            data: [
                                {
                                    label: '成功接通',
                                    value: 1250,
                                    color: '#52c41a',
                                },
                                {
                                    label: '未接听',
                                    value: 680,
                                    color: '#faad14',
                                },
                                {
                                    label: '拒绝接听',
                                    value: 320,
                                    color: '#ff7a45',
                                },
                                {
                                    label: '号码异常',
                                    value: 150,
                                    color: '#d9d9d9',
                                },
                            ],
                            size: 220,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // 小数据量饼图示例
    pieChartSmall: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135631',
            msgId: '5135641',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '今日外呼任务快速概览：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '今日外呼状态',
                            data: [
                                {
                                    label: '已完成',
                                    value: 25,
                                    color: '#52c41a',
                                },
                                {
                                    label: '进行中',
                                    value: 15,
                                    color: '#1890ff',
                                },
                                {
                                    label: '待开始',
                                    value: 10,
                                    color: '#faad14',
                                },
                            ],
                            size: 180,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
};
