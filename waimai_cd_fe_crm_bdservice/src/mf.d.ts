// see: https://dev.sankuai.com/code/repo-detail/wm/waimai_cd_fe_crm_ai/file/list?path=src%2Fcomponents%2FCreateTaskFederation&branch=refs%2Fheads%2Ffeature%2FCBDGF-2019-91293323%2Fmf-create-task
declare module 'crm-ai/CreateTaskFederation' {
    import * as React from 'react';

    interface CreateTaskFederationProps {
        /** 商家类型 */
        contactObjectType?: number;
        /** 商家ID列表（contactObjectIdList） */
        contactObjectIdList?: string;
        /** 任务名称 */
        taskName?: string;
        /** 触达类型 */
        reachType?: number;
        /** 触达对象 */
        kpPriority?: string[];
        /** 关联agent ID */
        agentId?: number;
        /** 场景ID（智能调度时使用） */
        sceneId?: number;
        /** 开始时间 */
        startTime?: string | number;
        /** 结束时间 */
        endTime?: string | number;
        /** 业务线ID */
        bizId?: number;
        /** 任务类型 */
        taskType?: string;
        /** 创建任务成功后的回调函数 */
        onCreateSuccess?: (taskId?: string | number) => void;
        /** 取消创建任务的回调函数 */
        onCancel?: () => void;
    }

    const CreateTaskFederation: React.FC<CreateTaskFederationProps>;
    export default CreateTaskFederation;
}
