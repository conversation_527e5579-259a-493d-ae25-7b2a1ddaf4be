const API_PATH = import.meta.env.VITE_API_PREFIX; // vite需要通过import.meta.env获取环境变量

export const getPrefix = (path: string, fallback = '') => {
    if (!API_PATH) {
        return '';
    }

    if (/^\/helpbd/.test(path) || /^\/livescript/.test(path)) {
        return API_PATH;
    }

    if (/^\/manage/.test(path) || /^\/common/.test(path)) {
        return `${API_PATH}/assistant`;
    }

    if (/^\/bee\/v1\/bdaiassistant/.test(path)) {
        return `${API_PATH}`;
    }

    if (/^\/bee\/v2/.test(path)) {
        return `${API_PATH}`;
    }

    if (/^\/impc/.test(path)) {
        return '/xianfu/api/dove';
    }

    if (path === '/dx/mock/pubMsg') {
        return '/xianfu/api/bdservice/assistant';
    }

    if (path === '/uicomponent/getLoginUser') {
        return '/xianfu/api/jaguar';
    }

    if (/^\/uicomponent/.test(path) || /^\/wm/.test(path)) {
        return '/xianfu/api/common';
    }

    if (/^\/product/.test(path)) {
        return '/xianfu/api';
    }

    return fallback;
};
