# 前端 UI 改版 2.0 需求分析报告

## 一、项目现状分析

### 1.1 技术栈概况

-   **框架**：React 18 + TypeScript + Vite
-   **UI 库**：Ant Design 5.x +内部 UI 框架@roo/roo
-   **状态管理**：Zustand
-   **样式方案**：SCSS + CSS 模块化

### 1.2 当前架构

-   **主入口**：`src/pages/knowledge/chat/main.tsx`
-   **核心组件**：WelcomeMessage、AiInput、Toolbar、TaskList
-   **状态管理**：集中在`common/data/core.ts`的 Zustand store

## 二、详细需求分析

### 2.1 首页结构重排需求

#### 现状分析

-   当前首页结构相对简单，主要包含欢迎信息、热门问题和输入框
-   缺乏明确的模块分区概念

#### 改造需求

1. **首页布局重构**

    - **顶部区域**: 包含动态问候语或 Banner。
    - **中部区域**: 核心功能区，以卡片形式分为“商家沟通”、“智能诊断”和“快捷查询”三大模块。
    - **底部区域**: 用户输入区及“猜你想问”。

2. **涉及文件**
    - `main.tsx:148-299` - 主要渲染逻辑需重构
    - `welcome.tsx` - 需要扩展支持 Banner 和技能分类
    - `welcome.scss` - 样式需要配合新布局调整

### 2.2 技能分类展示需求

#### 现状分析

-   当前工具栏(toolbar.tsx)只支持单一维度的工具展示
-   缺乏分类和权限控制机制

#### 改造需求

1. **卡片式分类体系**

    - 首页采用卡片式布局，承载主要功能入口（数据无需关心由后端下发）。
    - **商家沟通**: 包含自取外呼、报名外呼、商家营业、IM 群发等。
    - **智能诊断**: 包含新签商家、新商家考核、神枪手续效、商家诊断等。
    - **快捷查询**: 包含驳回查询、点评高分查询、到手价计算、短信查询等。
    - 每个分类卡片内部支持横向滑动；若功能项有子项，点击分类标题区域可弹出完整技能列表浮层（本期暂不需支持）。

2. **对话中上下文工具栏**

    - 用户发起对话后，首页的技能卡片切换为页面底部的紧凑型工具栏。

3. **涉及文件**
    - `toolbar.tsx:22-419` - 需要重构支持分类展示
    - `service/toolbar.ts` - 需要扩展 API 支持分类数据
    - 新增技能分类组件和样式文件

### 2.3 Banner 区动态文案需求

#### 现状分析

-   当前 welcome.tsx 已有天气文案基础(`welcomeRes.data.weatherTips`)
-   但缺乏动态 Banner 和按钮配置能力

#### 改造需求

1. **功能扩展**

    - 支持动态 Banner 内容（如："新'绩效功能'上线快来试试吧~"）、按钮文案（如："去试试"）。
    - 支持点击后自动发送 query。
    - 支持天气相关文案切换（如："今天下雪了记得保暖哦"）。
    - 天气文案和 Banner 互斥

2. **涉及文件**
    - `welcome.tsx:32-56` - fetchData 逻辑需扩展
    - 需要新增 Banner 组件
    - API 接口需要扩展 Banner 配置

### 2.4 主题色切换需求

#### 现状分析

-   当前主题色配置在`CustomConfigProvider.tsx:15`为黄色(`#FFcc33`)
-   链接色为橙色(`#FF6A00`)

#### 改造需求

1. **主色调切换**

    - 主色调：黄色 → 紫色
    - 需要更新所有相关组件、按钮、卡片、边框等

2. **涉及文件**
    - `CustomConfigProvider.tsx:15` - 主题色配置
    - 大量 SCSS 文件需要颜色变量统一管理
    - 建议引入 CSS 变量或主题配置文件

### 2.5 视觉改造需求

#### 现状分析

-   当前使用静态背景图片(`BgImgNew`)
-   形象使用 GIF 动图(`BeeGif`)

#### 改造需求

1. **动图背景**

    - 首页背景全部切换为动图。
    - 支持不同天气主题（如：根据天气接口返回数据显示下雪等动效）。

2. **涉及文件**
    - `main.tsx:157` - 背景图片配置
    - `welcome.tsx:3` - 形象动图配置
    - `assets/images/chat/` - 需要新增动图资源

### 2.6 公告栏优化需求

#### 现状分析

-   toolbar.tsx 已有公告栏实现(`announcement`)
-   支持展开/收起、关闭功能

#### 改造需求

1. **样式优化**

    - 支持高亮超链样式
    - 配合新主题色调整

2. **涉及文件**
    - `toolbar.tsx:188-274` - 公告栏渲染逻辑
    - 相关样式需要配合主题色调整

### 2.7 猜你想问功能需求

#### 现状分析

-   welcome.tsx 已有热门问题展示(`hotQuestions`)
-   支持最多展示问题列表

#### 改造需求

1. **逻辑优化**

    - 冷启动时展示"猜你想问"（最多 5 个），例如：“神枪手续效计算公式 →”、“新商家绩效诊断 →”。
    - 用户输入后切换为线上推荐逻辑

2. **涉及文件**

    - `welcome.tsx:26-106` - 热门问题逻辑需要扩展

3. 主题色改造设计的改造点

| 调整点                 | 具体说明                                                                         |
| ---------------------- | -------------------------------------------------------------------------------- |
| 主色调调整             | 小蜜视觉主色调由黄色改为紫色，所有相关组件、卡片、按钮、边框等需统一为紫色风格。 |
| 商家卡片底色优化       | 商家卡片外圈原有黄色底色去除，提升整体视觉简洁度。                               |
| 对话框底色优化         | 对话框底色需适配新主色调，统一风格。                                             |
| 思考圈圈样式调整       | “思考圈圈”组件颜色、动画效果需与新主色调保持一致。                               |
| 输入框及按钮边框调整   | 输入框、各类按钮、边框等细节统一为紫色，保证交互一致性。                         |
| 高亮超链样式优化       | 超链接高亮样式调整为紫色，提升可识别性。                                         |
| 卡片组件底色与按钮优化 | 所有卡片组件底色、按钮、点点等细节统一为紫色，风格一致。                         |

## 三、实施建议

### 3.1 优先级排序

1. **高优先级**：主题色切换、首页结构重排
2. **中优先级**：技能分类展示、Banner 区
3. **低优先级**：动图背景、引导功能

### 3.2 技术方案建议

1. **主题系统**：建议使用 CSS 变量统一管理颜色
2. **组件拆分**：将复杂功能拆分为独立组件
3. **状态管理**：扩展 Zustand store 支持新功能状态
4. **API 设计**：需要后端配合提供技能分类、权限、Banner 等接口

### 3.3 风险评估

1. **兼容性风险**：主题色大范围修改可能影响现有功能
2. **性能风险**：动图背景可能影响页面性能
3. **维护风险**：功能复杂度增加，需要完善的文档和测试

## 四、人力消耗预估

### 4.1 总体预估

-   **总工期**：15-18 PD（约 3-4 周）
-   **开发人员**：前端开发工程师 1 人
-   **风险缓冲**：20% 额外时间

### 4.2 详细工时分解

#### 4.2.1 基础架构改造（4-5 PD）

| 任务项                       | 预估工时 | 说明                                    |
| ---------------------------- | -------- | --------------------------------------- |
| 创建 PurpleThemeWrapper 组件 | 0.5 PD   | 局部主题包装组件开发                    |
| HomePageLayout 布局组件      | 1 PD     | 新的首页布局结构组件                    |
| 首页结构重构（main.tsx）     | 1 PD     | 重构渲染逻辑，集成新布局                |
| 基础样式调整                 | 1-1.5 PD | 逐个组件的紫色主题样式调整              |
| 组件拆分和优化               | 0.5-1 PD | WelcomeHeader、UserInputArea 等组件拆分 |

#### 4.2.2 技能分类展示系统（3-4 PD）

| 任务项                 | 预估工时 | 说明                               |
| ---------------------- | -------- | ---------------------------------- |
| SkillCardArea 组件开发 | 1.5 PD   | 技能卡片区域组件，包含横向滑动     |
| API 接口对接           | 0.5 PD   | 对接后端技能卡片配置接口           |
| 样式开发               | 1 PD     | 卡片样式、hover 效果、紫色主题适配 |
| 上下文工具栏           | 1 PD     | 对话中的紧凑型工具栏（简化版）     |

#### 4.2.3 Banner 区动态配置（2-3 PD）

| 任务项                 | 预估工时 | 说明                                |
| ---------------------- | -------- | ----------------------------------- |
| DynamicBanner 组件开发 | 1 PD     | Banner 组件，支持条件匹配和互斥逻辑 |
| WelcomeHeader 组件改造 | 0.5 PD   | 集成 Banner 和天气文案显示          |
| API 扩展对接           | 0.5 PD   | 对接扩展的 welcome 接口             |
| 样式和交互优化         | 0.5-1 PD | Banner 样式、按钮交互等             |

#### 4.2.4 主题色系统改造（2-3 PD）

| 任务项                 | 预估工时 | 说明                         |
| ---------------------- | -------- | ---------------------------- |
| 核心组件样式调整       | 1 PD     | welcome、热门问题等核心组件  |
| 按钮和表单元素调整     | 0.5 PD   | 输入框、按钮、边框等交互元素 |
| 卡片和容器组件调整     | 0.5 PD   | 各类卡片、对话框等容器组件   |
| 思考圈圈等动画组件调整 | 0.5 PD   | 加载动画、思考状态等特殊组件 |
| 超链接和高亮样式调整   | 0.5 PD   | 公告栏超链接、高亮文本等     |

#### 4.2.5 视觉优化和细节完善（2-3 PD）

| 任务项           | 预估工时 | 说明                                   |
| ---------------- | -------- | -------------------------------------- |
| 动图背景系统     | 1 PD     | 背景动图切换逻辑（简化版，仅默认背景） |
| 猜你想问功能优化 | 0.5 PD   | 冷启动逻辑和样式调整                   |
| 公告栏样式优化   | 0.5 PD   | 配合新主题色的样式调整                 |
| 细节交互完善     | 0.5-1 PD | hover 效果、过渡动画等细节             |

#### 4.2.6 测试和优化（2 PD）

| 任务项              | 预估工时 | 说明                       |
| ------------------- | -------- | -------------------------- |
| 功能测试和 bug 修复 | 1 PD     | 各功能模块的测试和问题修复 |
| 兼容性测试          | 0.5 PD   | 确保不影响现有功能         |
| 性能优化            | 0.5 PD   | 动图加载、组件渲染优化等   |

### 4.3 风险因素分析

#### 4.3.1 可能增加工时的因素

-   **样式冲突处理**：+1-2 PD（如果出现大量样式冲突）
-   **API 接口调整**：+0.5-1 PD（如果后端接口需要多次调整）
-   **交互细节完善**：+1 PD（如果需要更多的交互优化）
-   **兼容性问题**：+0.5-1 PD（如果出现意外的兼容性问题）

#### 4.3.2 可能节省工时的因素

-   **复用现有组件**：-0.5-1 PD（如果能更多复用现有代码）
-   **简化需求**：-1-2 PD（如果部分功能可以简化实现）

### 4.4 分阶段实施建议

#### 第一阶段（5-6 PD）- 核心功能

-   基础架构改造
-   主题色系统改造
-   首页布局重构

#### 第二阶段（6-7 PD）- 功能完善

-   技能分类展示系统
-   Banner 区动态配置
-   基础测试

#### 第三阶段（4-5 PD）- 优化完善

-   视觉优化和细节完善
-   全面测试和 bug 修复
-   性能优化

### 4.5 资源配置建议

-   **前端开发**：1 人全职投入
-   **设计支持**：0.2 人（视觉细节确认）
-   **后端支持**：0.3 人（API 接口开发）
-   **测试支持**：0.2 人（功能测试）

### 4.6 里程碑节点

-   **第 1 周末**：完成基础架构和主题色改造
-   **第 2 周末**：完成技能展示和 Banner 功能
-   **第 3 周末**：完成视觉优化和功能测试
-   **第 4 周初**：完成最终优化和上线准备

**注意**：以上预估基于当前需求理解，实际开发过程中可能因需求变更、技术难点等因素有所调整。建议预留 20% 的缓冲时间以应对不可预见的问题。
