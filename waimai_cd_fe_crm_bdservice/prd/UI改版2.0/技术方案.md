# UI 改版 2.0 技术方案

## 一、整体技术方案

### 1.1 架构设计原则

-   **渐进式改造**：基于现有架构逐步优化，降低风险
-   **组件化**：将新功能拆分为独立可复用组件
-   **统一管理**：建立统一的主题、状态、配置管理体系
-   **向后兼容**：确保改造过程中不影响现有功能

### 1.2 技术栈保持

-   React 18 + TypeScript + Vite
-   Ant Design 5.x + 内部 UI 框架
-   Zustand 状态管理
-   SCSS 样式方案

## 二、核心功能技术方案

### 2.1 主题色系统重构

#### 2.1.1 方案设计

建立基于 CSS 变量的主题系统，支持动态切换和统一管理。

#### 2.1.2 实现步骤

**步骤 1：明确视觉改造点**
根据 PRD 要求，主题色系统重构需要覆盖以下具体调整点：

-   **主色调统一**：将小蜜视觉主色调由黄色改为紫色，确保所有相关组件、卡片、按钮、边框等统一为紫色风格。
-   **卡片底色优化**：移除商家卡片外圈的黄色底色，提升视觉简洁度。
-   **对话框适配**：调整对话框底色，与新主色调统一。
-   **动画效果调整**：更新“思考圈圈”等动效组件的颜色，与新主色调保持一致。
-   **交互元素统一**：统一输入框、各类按钮、边框等细节为紫色。
-   **超链接样式**：将高亮超链样式调整为紫色，提升可识别性。

**步骤 2：创建主题配置文件**

```typescript
// src/theme/colors.ts
export const themeColors = {
    purple: {
        '--primary-color': '#8B5CF6', // 紫色主色调
        '--primary-hover': '#7C3AED',
        '--primary-active': '#6D28D9',
        '--link-color': '#8B5CF6',
        '--success-color': '#10B981',
        '--warning-color': '#F59E0B',
        '--error-color': '#EF4444',
    },
    yellow: {
        '--primary-color': '#FFcc33', // 原有黄色主色调
        '--primary-hover': '#FFB800',
        '--primary-active': '#E6A200',
        '--link-color': '#FF6A00',
        '--success-color': '#10B981',
        '--warning-color': '#F59E0B',
        '--error-color': '#EF4444',
    },
};

export type ThemeColorType = keyof typeof themeColors;
```

**步骤 3：创建主题 Provider**

```typescript
// src/components/theme/ThemeProvider.tsx
import React, { useEffect } from 'react';
import { themeColors, ThemeColorType } from '@/theme/colors';

interface ThemeProviderProps {
    theme?: ThemeColorType;
    children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
    theme = 'purple', // 默认使用紫色主题
    children,
}) => {
    useEffect(() => {
        const root = document.documentElement;
        const colors = themeColors[theme];

        Object.entries(colors).forEach(([key, value]) => {
            root.style.setProperty(key, value);
        });
    }, [theme]);

    return <>{children}</>;
};
```

**步骤 4：创建局部主题包装组件**

```typescript
// src/components/theme/PurpleThemeWrapper.tsx
import { ConfigProvider } from 'antd';
import React from 'react';

interface PurpleThemeWrapperProps {
    children: React.ReactNode;
}

export const PurpleThemeWrapper: React.FC<PurpleThemeWrapperProps> = ({ children }) => {
    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#8B5CF6',
                    colorLink: '#8B5CF6',
                },
                components: {
                    Button: {
                        colorPrimary: '#8B5CF6',
                        colorPrimaryHover: '#7C3AED',
                        colorPrimaryActive: '#6D28D9',
                    },
                    Card: {
                        colorBorderSecondary: '#8B5CF6',
                    },
                    Input: {
                        colorPrimary: '#8B5CF6',
                        colorPrimaryHover: '#7C3AED',
                    },
                },
            }}
        >
            {children}
        </ConfigProvider>
    );
};
```

**步骤 5：按组件逐步调整样式**

```scss
// 具体组件样式文件中直接使用紫色值

// src/pages/knowledge/chat/common/ui/message/welcome.scss
.hot-question-item {
    background-color: #8b5cf6; // 直接使用紫色
    &:hover {
        background-color: #7c3aed;
    }
}

// src/pages/knowledge/chat/components/SkillCardArea.scss
.skill-category-card {
    border-color: #8b5cf6;

    .skill-item {
        &:hover {
            background-color: rgba(139, 92, 246, 0.1);
        }
    }
}

// src/pages/knowledge/chat/components/DynamicBanner.scss
.dynamic-banner__button {
    background-color: #8b5cf6;
    border-color: #8b5cf6;

    &:hover {
        background-color: #7c3aed;
        border-color: #7c3aed;
    }
}
```

### 2.2 首页结构重排

#### 2.2.1 方案设计

重构 main.tsx 的布局结构，采用新的`HomePageLayout`组件，将功能区域明确分离为顶部 Banner、中部技能卡片和底部输入区。

#### 2.2.2 实现步骤

**步骤 1：创建布局容器组件**

```typescript
// src/pages/knowledge/chat/components/HomePageLayout.tsx
interface HomePageLayoutProps {
    topArea: React.ReactNode; // 顶部区域，用于Banner或问候语
    middleArea: React.ReactNode; // 中部区域，用于技能卡片
    bottomArea: React.ReactNode; // 底部区域，用于输入框和猜你想问
}

export const HomePageLayout: React.FC<HomePageLayoutProps> = ({ topArea, middleArea, bottomArea }) => {
    return (
        <div className="home-page-layout">
            <div className="home-page-layout__top">{topArea}</div>
            <div className="home-page-layout__middle">{middleArea}</div>
            <div className="home-page-layout__bottom">{bottomArea}</div>
        </div>
    );
};
```

**步骤 2：重构 main.tsx（使用局部主题包装）**

```typescript
// src/pages/knowledge/chat/main.tsx 修改renderContent方法
import { PurpleThemeWrapper } from '@/components/theme/PurpleThemeWrapper';

const renderContent = () => {
    return (
        <div className="chat-main-container">
            <ChatHeader />
            {showHome ? (
                <PurpleThemeWrapper>
                    <HomePageLayout
                        topArea={<WelcomeHeader />} // 包含问候语和Banner
                        middleArea={<SkillCardArea onSkillClick={handleSkillClick} />} // 技能卡片区
                        bottomArea={<UserInputArea onQuestionClick={onQuestionClick} />} // 输入区和猜你想问
                    />
                </PurpleThemeWrapper>
            ) : (
                <>
                    <DialogArea messageArray={finalMessageArray} /* ... */ />
                    <AiInputNew style={{ maxWidth: 800 }} />
                    <ContextualToolbar /> {/* 对话中的上下文工具栏 */}
                </>
            )}
        </div>
    );
};
```

**步骤 3：样式重构**

```scss
// src/pages/knowledge/chat/components/HomePageLayout.scss
.home-page-layout {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;

    &__top {
        /* 顶部区域样式 */
    }

    &__middle {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        /* 中部区域样式 */
    }

    &__bottom {
        /* 底部区域样式 */
    }
}
```

### 2.3 技能分类展示系统

#### 2.3.1 方案设计

重构技能展示部分，创建一个新的`SkillCardArea`组件，该组件负责从后端获取已经过权限处理的技能卡片数据并渲染。移除前端复杂的权限判断和弹窗逻辑，简化实现。

#### 2.3.2 实现步骤

**步骤 1：调整数据结构**

```typescript
// src/pages/knowledge/chat/common/type/skill.ts
export interface Skill {
    id: string;
    name: string;
    icon?: string;
    // ... 其他业务字段
}

export interface SkillCategory {
    id: string;
    name: string;
    skills: Skill[];
}

// 后端直接返回完整的、可供展示的技能列表
export interface SkillCardResponse {
    categories: SkillCategory[];
}
```

**步骤 2：创建技能卡片区域组件**

```typescript
// src/pages/knowledge/chat/components/SkillCardArea.tsx
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';

interface SkillCardAreaProps {
    onSkillClick: (skill: Skill) => void;
}

const fetchSkillCards = async () => {
    const res = await apiCaller.send<SkillCardResponse>('/bee/v2/bdaiassistant/uiconfig/skillcards');
    if (res.code !== 0) {
        // 适当的错误处理
        return { categories: [] };
    }
    return res.data;
};

export const SkillCardArea: React.FC<SkillCardAreaProps> = ({ onSkillClick }) => {
    const { data, loading } = useRequest(fetchSkillCards);

    if (loading) {
        return <Spin />; // 或其他加载状态
    }

    return (
        <div className="skill-card-area">
            {data?.categories.map(category => (
                <Card title={category.name} key={category.id} className="skill-category-card">
                    <div className="skill-items-container">
                        {/* 横向滚动列表 */}
                        {category.skills.map(skill => (
                            <div key={skill.id} className="skill-item" onClick={() => onSkillClick(skill)}>
                                {skill.icon && <img src={skill.icon} alt={skill.name} />}
                                <span>{skill.name}</span>
                            </div>
                        ))}
                    </div>
                </Card>
            ))}
        </div>
    );
};
```

**步骤 3：移除不再需要的 Hook**
删除 `src/pages/knowledge/chat/hooks/useSkills.ts` 文件及其中的 `useSkills` 和 `hasPermission` 逻辑，因为权限判断已移至后端处理。

### 2.4 Banner 区动态配置

#### 2.4.1 方案设计

扩展现有`welcome`组件，支持动态 Banner 配置。同时根据 PRD，实现天气文案和 Banner 的互斥显示逻辑。

#### 2.4.2 实现步骤

**步骤 1：扩展 API 数据结构**
(保持不变, `WelcomeData`中已包含`banners`和`weatherTips`)

**步骤 2：创建 Banner 组件**

```typescript
// src/pages/knowledge/chat/components/DynamicBanner.tsx
interface DynamicBannerProps {
    banners: BannerConfig[];
    weather: { condition: string; temperature: number };
    weatherTips?: string; // 新增 weatherTips
    onButtonClick: (query: string) => void;
}

export const DynamicBanner: React.FC<DynamicBannerProps> = ({
    banners,
    weather,
    weatherTips, // 新增 weatherTips
    onButtonClick,
}) => {
    const activeBanner = useMemo(() => {
        const validBanners = banners
            .filter(banner => banner.enabled)
            .filter(banner => {
                if (!banner.conditions) return true;

                // 天气条件匹配
                if (banner.conditions.weather) {
                    return banner.conditions.weather.includes(weather.condition);
                }

                // 时间条件匹配
                if (banner.conditions.timeRange) {
                    const now = new Date().getHours();
                    const [start, end] = banner.conditions.timeRange;
                    return now >= parseInt(start) && now <= parseInt(end);
                }

                return true;
            })
            .sort((a, b) => b.priority - a.priority);

        return validBanners[0];
    }, [banners, weather]);

    // PRD新规则：如果显示了Banner，则不显示天气文案
    if (activeBanner) {
        return (
            <div className="dynamic-banner">
                <div className="dynamic-banner__content">
                    <h3 className="dynamic-banner__title">{activeBanner.title}</h3>
                    <p className="dynamic-banner__text">{activeBanner.content}</p>
                </div>
                {activeBanner.buttonText && activeBanner.buttonQuery && (
                    <button className="dynamic-banner__button" onClick={() => onButtonClick(activeBanner.buttonQuery!)}>
                        {activeBanner.buttonText}
                    </button>
                )}
            </div>
        );
    }

    // 如果没有Banner，则显示天气文案
    if (weatherTips) {
        return (
            <div className="weather-tips-container">
                <pre className="welcome-text">{weatherTips}</pre>
            </div>
        );
    }

    return null;
};
```

**步骤 3：更新 Welcome 组件/Header 组件**

```typescript
// src/pages/knowledge/chat/components/WelcomeHeader.tsx
const WelcomeHeader: React.FC<WelcomeHeaderProps> = ({ onBannerClick }) => {
    const [welcomeData, setWelcomeData] = useState<WelcomeData>();

    // ... fetchData 逻辑 ...

    return (
        <div className="welcome-header-container">
            <div className="welcome-text-container">
                <pre className="welcome-text">{welcomeData?.greeting}</pre>
            </div>

            {/* 天气文案和Banner的显示逻辑已移入DynamicBanner组件 */}
            <DynamicBanner
                banners={welcomeData?.banners || []}
                weather={welcomeData?.weather}
                weatherTips={welcomeData?.weatherTips}
                onButtonClick={onBannerClick}
            />
        </div>
    );
};
```

### 2.5 动图背景系统

#### 2.5.1 方案设计

基于天气和时间条件，动态切换背景动图，优化性能和用户体验。

#### 2.5.2 实现步骤

**步骤 1：背景资源管理（PC 端无天气信息，只使用默认背景）**

```typescript
// src/assets/backgrounds/index.ts
export const backgroundAssets = {
    default: () => import('./default.gif'),
    sunny: () => import('./sunny.gif'),
    rainy: () => import('./rainy.gif'),
    snowy: () => import('./snowy.gif'),
    cloudy: () => import('./cloudy.gif'),
};

export type BackgroundType = keyof typeof backgroundAssets;
```

**步骤 2：背景管理 Hook（PC 端无天气信息，只使用默认背景）**

```typescript
// src/pages/knowledge/chat/hooks/useBackground.ts
export const useBackground = (weather?: { condition: string }) => {
    const [currentBg, setCurrentBg] = useState<string>();
    const [isLoading, setIsLoading] = useState(false);

    const loadBackground = useCallback(async (type: BackgroundType) => {
        setIsLoading(true);
        try {
            const bgModule = await backgroundAssets[type]();
            setCurrentBg(bgModule.default);
        } catch (error) {
            console.error('背景加载失败:', error);
            // 降级到默认背景
            const defaultBg = await backgroundAssets.default();
            setCurrentBg(defaultBg.default);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        if (!weather) {
            loadBackground('default');
            return;
        }

        const weatherMap: Record<string, BackgroundType> = {
            sunny: 'sunny',
            rainy: 'rainy',
            snowy: 'snowy',
            cloudy: 'cloudy',
        };

        const bgType = weatherMap[weather.condition] || 'default';
        loadBackground(bgType);
    }, [weather, loadBackground]);

    return { background: currentBg, isLoading };
};
```

**步骤 3：更新主组件**

```typescript
// src/pages/knowledge/chat/main.tsx 修改
const Chat = () => {
    const { data: bizInfo } = useBizInfo();
    const { background, isLoading } = useBackground(bizInfo?.weather);

    const renderContent = () => {
        return (
            <div
                style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    paddingBottom: 20,
                    backgroundImage: background ? `url(${background})` : undefined,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'cover',
                    boxShadow: '0px 10px 22px 1px rgba(100, 100, 100, 0.23)',
                }}
                className={isLoading ? 'loading-background' : ''}
            >
                {/* 现有内容 */}
            </div>
        );
    };

    // ...
};
```

## 三、状态管理方案

### 3.1 扩展 Zustand Store

```typescript
// src/pages/knowledge/chat/common/data/core.ts 扩展
import { ThemeColorType } from '@/theme/colors';

interface AiStore {
    // 现有状态...

    // 新增状态
    theme: {
        colorType: ThemeColorType; // 当前使用的颜色主题
        primaryColor: string; // 当前主色调值
    };

    // 新增方法
    setTheme: (colorType: ThemeColorType) => void;
}

const useAiStore = create<AiStore>((set, get) => ({
    // 现有状态...

    theme: {
        colorType: 'purple',
        primaryColor: '#8B5CF6',
    },

    setTheme: colorType =>
        set(state => ({
            theme: {
                colorType,
                primaryColor: themeColors[colorType]['--primary-color'],
            },
        })),
}));
```

## 四、API 接口设计

### 4.1 需要调整的接口

```typescript
// 技能卡片配置接口 (新)
// 合并了原有的技能分类和用户权限接口
GET /bee/v2/bdaiassistant/uiconfig/skillcards
Response: {
  code: number;
  data: {
    categories: SkillCategory[]; // 后端完成权限过滤和排序
  };
}

// 扩展欢迎信息接口 (保持不变)
GET /bee/v2/bdaiassistant/common/welcome
Response: {
  code: number;
  data: {
    greeting: string;
    weatherTips: string;
    banners: BannerConfig[];
    weather: {
      condition: string;
      temperature: number;
    };
  };
}
```

## 五、实施计划

### 5.1 第一阶段 (1-2 周) - 基础架构

-   [ ] 创建局部主题包装组件 `PurpleThemeWrapper`
-   [ ] 首页布局结构调整
-   [ ] 基础组件拆分
-   [ ] 逐个组件样式调整（welcome、技能卡片等）

### 5.2 第二阶段 (2-3 周) - 核心功能

-   [ ] 技能分类展示系统
-   [ ] Banner 动态配置
-   [ ] 权限控制集成

### 5.3 第三阶段 (1-2 周) - 视觉优化

-   [ ] 动图背景系统
-   [ ] 公告栏样式优化
-   [ ] 细节交互完善

### 5.4 第四阶段 (1 周) - 测试优化

-   [ ] 功能测试
-   [ ] 性能优化
-   [ ] 兼容性验证

## 六、风险控制

### 6.1 技术风险

-   **主题切换影响**：通过局部主题包装组件，仅影响特定页面，确保其他页面不受影响
-   **样式冲突风险**：逐个组件调整样式，需要充分测试避免样式冲突
-   **性能问题**：动图背景懒加载，支持降级策略
-   **状态管理复杂度**：保持现有 store 结构，仅扩展新功能

### 6.2 业务风险

-   **权限控制**：与后端充分对接，确保权限逻辑正确
-   **用户体验**：保持现有交互习惯，新功能渐进式引导

### 6.3 维护风险

-   **文档完善**：及时更新组件文档和使用说明
-   **代码规范**：遵循现有代码风格和命名规范
-   **测试覆盖**：为新功能编写充分的单元测试

## 七、后续优化建议

1. **监控体系**：建立用户行为埋点，监控新功能使用情况
2. **A/B 测试**：对比新旧版本用户体验指标
3. **性能监控**：关注页面加载时间和交互响应速度
4. **用户反馈**：收集用户使用反馈，持续优化功能体验
