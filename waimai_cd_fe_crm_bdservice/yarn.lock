# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ai/mime-types-web@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ai/mime-types-web/download/@ai/mime-types-web-1.1.2.tgz"
  integrity sha1-azZxpK+tNzD8CI/WmiCIEV7TF5Y=

"@ai/mss-upload-js@^1.1.6-beta13", "@ai/mss-upload-js@^1.1.7":
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/@ai/mss-upload-js/download/@ai/mss-upload-js-1.1.8.tgz"
  integrity sha1-1K0EEXzChvTaPU4R+wSSAmp9HSM=
  dependencies:
    "@ai/mime-types-web" "^1.1.2"
    "@babel/polyfill" "^7.12.1"
    crypto-js "^3.3.0"
    fast-xml-parser "^4.2.2"
    magic-bytes.js "^1.0.14"
    rollup-plugin-terser "^7.0.2"

"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.2.1":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.2.0":
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.2.0.tgz"
  integrity sha1-gNcyXSBGPwnHg50o2mMAQ91cJjo=
  dependencies:
    "@ant-design/fast-color" "^2.0.6"

"@ant-design/cssinjs-utils@^1.1.3":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs-utils/download/@ant-design/cssinjs-utils-1.1.3.tgz"
  integrity sha1-XdeRJgV5IKaZLVezjdhOLAtweXc=
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    rc-util "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.22.0":
  version "1.22.1"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs/download/@ant-design/cssinjs-1.22.1.tgz"
  integrity sha1-AOlDpjh6gICrqLkn34I23z4H6WQ=
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    classnames "^2.3.1"
    csstype "^3.1.3"
    rc-util "^5.35.0"
    stylis "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz"
  integrity sha1-q01EVcFULJAX02fC+oyj5CFdC6I=
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/icons-svg@^4.4.0":
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/@ant-design/icons-svg/download/@ant-design/icons-svg-4.4.2.tgz"
  integrity sha1-7Svn+02CrH4dRaVKWwbWzs+L5vY=

"@ant-design/icons@^5.2.6", "@ant-design/icons@^5.5.2":
  version "5.5.2"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.5.2.tgz"
  integrity sha1-xFZ5Q8wrfG2+nK5owG/6NfdV3A0=
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    classnames "^2.2.6"
    rc-util "^5.31.1"

"@ant-design/plots@^1.2.5":
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/@ant-design/plots/download/@ant-design/plots-1.2.6.tgz"
  integrity sha1-NeVBSEBpJJmJ+St9nNyDXqhxkcU=
  dependencies:
    "@antv/g2plot" "^2.2.11"
    "@antv/util" "^2.0.9"
    react-content-loader "^5.0.4"

"@ant-design/react-slick@~1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ant-design/react-slick/download/@ant-design/react-slick-1.1.2.tgz"
  integrity sha1-+Ezj5NDclB8CsW8dHW16Nx/7tPE=
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    resize-observer-polyfill "^1.5.1"
    throttle-debounce "^5.0.0"

"@antv/adjust@^0.2.1":
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/@antv/adjust/download/@antv/adjust-0.2.5.tgz"
  integrity sha1-uze7SgqHyj9LZghIvJrAfwK89ds=
  dependencies:
    "@antv/util" "~2.0.0"
    tslib "^1.10.0"

"@antv/attr@^0.3.1":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@antv/attr/download/@antv/attr-0.3.5.tgz"
  integrity sha1-BwjHT+1a1u4DrR4pEwme2CSPfr8=
  dependencies:
    "@antv/color-util" "^2.0.1"
    "@antv/scale" "^0.3.0"
    "@antv/util" "~2.0.0"
    tslib "^2.3.1"

"@antv/color-util@^2.0.1", "@antv/color-util@^2.0.2", "@antv/color-util@^2.0.3", "@antv/color-util@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@antv/color-util/download/@antv/color-util-2.0.6.tgz"
  integrity sha1-XhKbuc4/K5MJtSECs9ySlDDMwBY=
  dependencies:
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/component@^0.8.27":
  version "0.8.35"
  resolved "http://r.npm.sankuai.com/@antv/component/download/@antv/component-0.8.35.tgz"
  integrity sha1-HVuOEb1JbLUF5kb1BfX1jwxRc+k=
  dependencies:
    "@antv/color-util" "^2.0.3"
    "@antv/dom-util" "~2.0.1"
    "@antv/g-base" "^0.5.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.7"
    "@antv/scale" "~0.3.1"
    "@antv/util" "~2.0.0"
    fecha "~4.2.0"
    tslib "^2.0.3"

"@antv/coord@^0.3.0":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@antv/coord/download/@antv/coord-0.3.1.tgz"
  integrity sha1-mC4mHYoeBqGY61GOp6zCDth1oBk=
  dependencies:
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/util" "~2.0.12"
    tslib "^2.1.0"

"@antv/dom-util@^2.0.2", "@antv/dom-util@~2.0.1":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@antv/dom-util/download/@antv/dom-util-2.0.4.tgz"
  integrity sha1-sJtWxW/sQolvyFbtrVa1lbR6tRQ=
  dependencies:
    tslib "^2.0.3"

"@antv/event-emitter@^0.1.1", "@antv/event-emitter@^0.1.2", "@antv/event-emitter@~0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@antv/event-emitter/download/@antv/event-emitter-0.1.3.tgz"
  integrity sha1-PgYyO53NVaMkHdx8VFjPq9IJUWQ=

"@antv/g-base@^0.5.11", "@antv/g-base@^0.5.12", "@antv/g-base@^0.5.9", "@antv/g-base@~0.5.6":
  version "0.5.16"
  resolved "http://r.npm.sankuai.com/@antv/g-base/download/@antv/g-base-0.5.16.tgz"
  integrity sha1-IqDLv8gQ5ikuTSXlcI0KvhZZEr8=
  dependencies:
    "@antv/event-emitter" "^0.1.1"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.13"
    "@types/d3-timer" "^2.0.0"
    d3-ease "^1.0.5"
    d3-interpolate "^3.0.1"
    d3-timer "^1.0.9"
    detect-browser "^5.1.0"
    tslib "^2.0.3"

"@antv/g-canvas@~0.5.10":
  version "0.5.17"
  resolved "http://r.npm.sankuai.com/@antv/g-canvas/download/@antv/g-canvas-0.5.17.tgz"
  integrity sha1-Lg0mOjVeFnudpeYG+9GtFQBHT88=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"
    tslib "^2.0.3"

"@antv/g-math@^0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@antv/g-math/download/@antv/g-math-0.1.9.tgz"
  integrity sha1-H5gbmuv1wCTyhDiao+XLqM76Hyg=
  dependencies:
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"

"@antv/g-svg@~0.5.6":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@antv/g-svg/download/@antv/g-svg-0.5.7.tgz"
  integrity sha1-1j21+FkKXzzqsJfBg+yA7RQ/ClA=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/util" "~2.0.0"
    detect-browser "^5.0.0"
    tslib "^2.0.3"

"@antv/g2@^4.1.26":
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/@antv/g2/download/@antv/g2-4.2.11.tgz"
  integrity sha1-o7JXrKTbYASgx/4ALcknJ5X5wYs=
  dependencies:
    "@antv/adjust" "^0.2.1"
    "@antv/attr" "^0.3.1"
    "@antv/color-util" "^2.0.2"
    "@antv/component" "^0.8.27"
    "@antv/coord" "^0.3.0"
    "@antv/dom-util" "^2.0.2"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "~0.5.6"
    "@antv/g-canvas" "~0.5.10"
    "@antv/g-svg" "~0.5.6"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.15"
    "@antv/scale" "^0.3.14"
    "@antv/util" "~2.0.5"
    tslib "^2.0.0"

"@antv/g2plot@^2.2.11", "@antv/g2plot@^2.4.31":
  version "2.4.32"
  resolved "http://r.npm.sankuai.com/@antv/g2plot/download/@antv/g2plot-2.4.32.tgz"
  integrity sha1-H+zEvMqCp5D8Tl5faqwDuUaaAS4=
  dependencies:
    "@antv/color-util" "^2.0.6"
    "@antv/event-emitter" "^0.1.2"
    "@antv/g-base" "^0.5.11"
    "@antv/g2" "^4.1.26"
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/path-util" "^3.0.1"
    "@antv/scale" "^0.3.18"
    "@antv/util" "^2.0.17"
    d3-hierarchy "^2.0.0"
    d3-regression "^1.3.5"
    fmin "^0.0.2"
    pdfast "^0.2.0"
    size-sensor "^1.0.1"
    tslib "^2.0.3"

"@antv/matrix-util@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.0.4.tgz"
  integrity sha1-6hPxWKovtLovuNa2tWHsRn6jrCA=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.3.0"
    tslib "^2.0.3"

"@antv/matrix-util@^3.1.0-beta.1", "@antv/matrix-util@^3.1.0-beta.2", "@antv/matrix-util@^3.1.0-beta.3":
  version "3.1.0-beta.3"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.1.0-beta.3.tgz"
  integrity sha1-4GHej6e+BGBaFVxpzFzpCC7t3e4=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.4.3"
    tslib "^2.0.3"

"@antv/path-util@^2.0.15", "@antv/path-util@~2.0.5", "@antv/path-util@~2.0.7":
  version "2.0.15"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-2.0.15.tgz"
  integrity sha1-pvaR38i3vOW+fwqrtb1hSWQyVjE=
  dependencies:
    "@antv/matrix-util" "^3.0.4"
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/path-util@^3.0.1":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-3.0.1.tgz"
  integrity sha1-Qv2EIigk6K2NG95w8WSgWSSHDUo=
  dependencies:
    gl-matrix "^3.1.0"
    lodash-es "^4.17.21"
    tslib "^2.0.3"

"@antv/scale@^0.3.0", "@antv/scale@^0.3.14", "@antv/scale@^0.3.18", "@antv/scale@~0.3.1":
  version "0.3.18"
  resolved "http://r.npm.sankuai.com/@antv/scale/download/@antv/scale-0.3.18.tgz"
  integrity sha1-uRH0MbPguVR7amX2bQ0/opW17zI=
  dependencies:
    "@antv/util" "~2.0.3"
    fecha "~4.2.0"
    tslib "^2.0.0"

"@antv/util@^2.0.17", "@antv/util@^2.0.9", "@antv/util@~2.0.0", "@antv/util@~2.0.12", "@antv/util@~2.0.13", "@antv/util@~2.0.3", "@antv/util@~2.0.5":
  version "2.0.17"
  resolved "http://r.npm.sankuai.com/@antv/util/download/@antv/util-2.0.17.tgz"
  integrity sha1-6O9CrKeJKBWyKSafPdEMazx1l6k=
  dependencies:
    csstype "^3.0.8"
    tslib "^2.0.3"

"@babel/cli@^7.8.0":
  version "7.26.4"
  resolved "http://r.npm.sankuai.com/@babel/cli/download/@babel/cli-7.26.4.tgz"
  integrity sha1-QQH/juXehEemw5U5epeSEFZBHSA=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    commander "^6.2.0"
    convert-source-map "^2.0.0"
    fs-readdir-recursive "^1.1.0"
    glob "^7.2.0"
    make-dir "^2.1.0"
    slash "^2.0.0"
  optionalDependencies:
    "@nicolo-ribaudo/chokidar-2" "2.1.8-no-fsevents.3"
    chokidar "^3.6.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz"
  integrity sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.26.0", "@babel/compat-data@^7.26.5":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.26.5.tgz"
  integrity sha1-35OsN/RBeFQTDiHXLGb/PUuJf8c=

"@babel/core@^7.21.3", "@babel/core@^7.7.5":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.26.0.tgz"
  integrity sha1-14tgI8yPMRTM8EnrIZYT90p0e0A=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.26.0", "@babel/generator@^7.26.5", "@babel/generator@^7.7.4":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.26.5.tgz"
  integrity sha1-5E1KsxdrvK94pXJdpfHcKIAqlFg=
  dependencies:
    "@babel/parser" "^7.26.5"
    "@babel/types" "^7.26.5"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.25.9":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.26.5.tgz"
  integrity sha1-ddkruNjVEwHA1J5Splyaf+lFFNg=
  dependencies:
    "@babel/compat-data" "^7.26.5"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.25.9.tgz"
  integrity sha1-dkQUdwa7kP9hMpfUntUma95yn4M=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.26.3.tgz"
  integrity sha1-UWl1bsvh2V94ZrkLtVWwIllTAqA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.2", "@babel/helper-define-polyfill-provider@^0.6.3":
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.3.tgz"
  integrity sha1-9PJ5L64u84IHS8LXE1Is8k5t2yE=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.25.9.tgz"
  integrity sha1-nf/+RvcnAFpeopBRrINftzXkwaM=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz"
  integrity sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.25.9", "@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz"
  integrity sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.25.9.tgz"
  integrity sha1-MySuULrn4qs8M/YMmod7agFGtU4=
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.26.5", "@babel/helper-plugin-utils@^7.8.0":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.26.5.tgz"
  integrity sha1-GFgNAMmTQRetcZOSxPZYXJMzzDU=

"@babel/helper-remap-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.25.9.tgz"
  integrity sha1-5TlWqz1bn7iL4Es+LzG1I6/TS5I=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-wrap-function" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-replace-supers@^7.25.9":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.26.5.tgz"
  integrity sha1-bLBOgq4pHa6OcjNd/kOLByXxTI0=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.26.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.25.9.tgz"
  integrity sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz"
  integrity sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz"
  integrity sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz"
  integrity sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=

"@babel/helper-wrap-function@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.25.9.tgz"
  integrity sha1-2Z39WVMS5siUvX0jdHACXIXuqdA=
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helpers@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.26.0.tgz"
  integrity sha1-MOYh8eulqkX+b0ho0ukVTYhBGaQ=
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.25.4", "@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.5", "@babel/parser@^7.7.4":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.26.5.tgz"
  integrity sha1-b+ya693vJcpXqTXIbbuRWuLaPh8=
  dependencies:
    "@babel/types" "^7.26.5"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz"
  integrity sha1-zC5T6/CgNAd3//XtUhlD4lO02P4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz"
  integrity sha1-r55PtjzLiry5I3Wy/P42tgx3TTA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz"
  integrity sha1-6Nwm/NYW5sW/K9DVosFR1PkqkTc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz"
  integrity sha1-gHpmf5FYrKxvYWS0vrha2evJ4dE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz"
  integrity sha1-3nCT8efer2jq3XzGsH8quCVDJp4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-proposal-class-properties@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.25.9.tgz"
  integrity sha1-hoBwf5Q9Gj2izWa5SBeZIPCX4lQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-decorators" "^7.25.9"

"@babel/plugin-proposal-export-default-from@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.25.9.tgz"
  integrity sha1-UnAr5u+DZ/yPGLhDgngzK+64+Hw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-decorators@^7.25.9", "@babel/plugin-syntax-decorators@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.25.9.tgz"
  integrity sha1-mGtMqLe13z9nzuiJzt7/wuK/FLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-dynamic-import@^7.7.4":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-flow@^7.25.9":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.26.0.tgz"
  integrity sha1-llB1lcIbRfzPwrx1jVxFRS5hZPo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-assertions@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.26.0.tgz"
  integrity sha1-YgQSQFBY76VuSlZJA7eTVQIPRF8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-attributes@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.26.0.tgz"
  integrity sha1-OxQShHaZ7qc5tPJgLHTONvawsPc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-jsx@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.25.9.tgz"
  integrity sha1-o0MToXjqVvGVFZm5KcHOrO5xkpA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.25.9.tgz"
  integrity sha1-eCHUQQvuXaqtu0zdmmZJcE4XaEU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-async-generator-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.25.9.tgz"
  integrity sha1-GxhTCwd9GKQHxJTrPR1y2lBSg6I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.25.9.tgz"
  integrity sha1-yAAI2srlFIJ5PlqcCLOaW+fhLXE=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"

"@babel/plugin-transform-block-scoped-functions@^7.25.9":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.26.5.tgz"
  integrity sha1-PcRAXTGtHL5FKTqlcgWm47AJ1T4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.26.5"

"@babel/plugin-transform-block-scoping@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.25.9.tgz"
  integrity sha1-wzZl5GsGdZyTaHyg+EOVuAwEc6E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.25.9.tgz"
  integrity sha1-qM6E/tua1RJUmYQQH6hAgKn19R8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-static-block@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.26.0.tgz"
  integrity sha1-bI2iGfTrFcrpg07ENI/46eCWZKA=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-classes@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.25.9.tgz"
  integrity sha1-cVJFf3iAtZOmOt6Khh5uJqRGn1I=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.25.9.tgz"
  integrity sha1-2zZJLHhGDlNLiFKx1b7+PJI+8Qs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/template" "^7.25.9"

"@babel/plugin-transform-destructuring@^7.25.9", "@babel/plugin-transform-destructuring@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.25.9.tgz"
  integrity sha1-lm6iWVxJgiQ0CINgLTz9egx5zqE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dotall-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.25.9.tgz"
  integrity sha1-uteUXdB3NMpS/jrU6HK0DtCbsJo=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-keys@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.25.9.tgz"
  integrity sha1-iFDd9X3OKuu0OUu0NKdZgDEFnm0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha1-b3JZtN4SdyGgjx5RZbhS/KppbTE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dynamic-import@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.25.9.tgz"
  integrity sha1-I+kX3mPtI8ZgDF3QbZRmnc5597g=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-exponentiation-operator@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.26.3.tgz"
  integrity sha1-4p8Btt4wLHwseUJ3pI8Eqcp/A7w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-export-namespace-from@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.25.9.tgz"
  integrity sha1-kHRf5VBTOU9VTkBYTNqB8sikAqI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-flow-strip-types@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.25.9.tgz"
  integrity sha1-hYebQqj1lI/WMXBpl46Y8j74rsE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-flow" "^7.25.9"

"@babel/plugin-transform-for-of@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.25.9.tgz"
  integrity sha1-S9x9QqITOXkF2J8CNQxSZ4ZtV1U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-function-name@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.25.9.tgz"
  integrity sha1-k52VbmimBmYQBb/VUMT8Lvlfe5c=
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-json-strings@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.25.9.tgz"
  integrity sha1-yG20B8uCfN7ZAqkMcH0ngaqolmA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.25.9.tgz"
  integrity sha1-GhxrTUqlm8TK1bazoiOgq9aFyd4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-logical-assignment-operators@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.25.9.tgz"
  integrity sha1-sZRBqMOaL9oJApALMG6gWuEFXbc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-member-expression-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.25.9.tgz"
  integrity sha1-Y9/xl2PqZKMfXmwglX5qJeQe1d4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-amd@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.25.9.tgz"
  integrity sha1-SbpHjyKVEBVEq9eUSGzTCI3dtsU=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-commonjs@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.26.3.tgz"
  integrity sha1-jwEdRLINAsPeRNiFDZcdhJf5gfs=
  dependencies:
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-systemjs@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.25.9.tgz"
  integrity sha1-i9G0ODYmnj0zMHFRoRS887pnk/g=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-modules-umd@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.25.9.tgz"
  integrity sha1-ZxAHnN18aU2zZSmh6EEeSfy/FMk=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha1-RUmQrmzCL9Kg+mCzosb2OjgGTmo=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-new-target@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.25.9.tgz"
  integrity sha1-QuYXESlLEFwkgzbcsEt3BU6ovs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-nullish-coalescing-operator@^7.25.9":
  version "7.26.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.26.6.tgz"
  integrity sha1-+/azySy1CeezGe5G49qJxb7dMf4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.26.5"

"@babel/plugin-transform-numeric-separator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.25.9.tgz"
  integrity sha1-v+11hmJhqLZDRosMz9J18gMyFKE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-object-rest-spread@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.25.9.tgz"
  integrity sha1-AgNyUCUHQWSAi88aLPqQxlLJnxg=
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"

"@babel/plugin-transform-object-super@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.25.9.tgz"
  integrity sha1-OF1d4TUWKTO+tKPSJ6K35Su0zwM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"

"@babel/plugin-transform-optional-catch-binding@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.25.9.tgz"
  integrity sha1-EOcNltUrsfEMXKqsWaxUXqK6f/M=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.25.9.tgz"
  integrity sha1-4ULriZ0m73FUNfIBq24TlUHu590=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-parameters@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.25.9.tgz"
  integrity sha1-uFaEIgWz534Yt6ehuUlYBpx7olc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-methods@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.25.9.tgz"
  integrity sha1-hH9BOSY1d1JkVdfTIjzYvaUeO1c=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-property-in-object@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.25.9.tgz"
  integrity sha1-nItz5k5sw8uydDYziFp90sOF/jM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-property-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.25.9.tgz"
  integrity sha1-1y1Yi9iLDeyLYuNvb9qRzt/ijj8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-display-name@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.25.9.tgz"
  integrity sha1-S3l0a1nvofOMhpUGWpKp9a+yT30=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-development@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.25.9.tgz"
  integrity sha1-j9Igp33ROcB+JSJakDuL6Mgp4Nc=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.25.9"

"@babel/plugin-transform-react-jsx@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.25.9.tgz"
  integrity sha1-BjZ5QNgyWzbt/14rnL54KUfKQWY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/plugin-transform-react-pure-annotations@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.25.9.tgz"
  integrity sha1-6hwRsvnbuOLZcCX0OjtbxH4YrmI=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-regenerator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.25.9.tgz"
  integrity sha1-A6ikZw1s666VMFrG3vrIHs53dAs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-regexp-modifiers@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.26.0.tgz"
  integrity sha1-L1g3pbXNOEKpGdgUfpkDzHRVuFA=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-reserved-words@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.25.9.tgz"
  integrity sha1-A5iu0vHxC6P3ipPbIZsn70F/uc4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-runtime@^7.22.9", "@babel/plugin-transform-runtime@^7.23.6":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.25.9.tgz"
  integrity sha1-YnI+o/WzH/vmdtqdba4XE4rlgOo=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.25.9.tgz"
  integrity sha1-u3heYJH5n4JqlfmJT8Fv3mHBY/I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-spread@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.25.9.tgz"
  integrity sha1-JKNRU5MbS6PRPOxKd0jCGrVRTvk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-sticky-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.25.9.tgz"
  integrity sha1-x/ArlE6YakF4F7ILosUE38FFPTI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-template-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.25.9.tgz"
  integrity sha1-bb1KJOj60CTfdtH6xqA89BP2D+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typeof-symbol@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.25.9.tgz"
  integrity sha1-IkukipKGndv4H5tKXxIEu/WivEs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-escapes@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.25.9.tgz"
  integrity sha1-p17zlHzhU2P8yqOOLdm8cLJ4i4I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-property-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.25.9.tgz"
  integrity sha1-qQHpbywdBxsNG7XcDTyIDOj1PdM=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.25.9.tgz"
  integrity sha1-Xq50f+OerPE6i9AGpPsLXR+l6bE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-sets-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.25.9.tgz"
  integrity sha1-ZRFMF7T/wg+lsWPGPHDA0lYh+r4=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/polyfill@^7.12.1", "@babel/polyfill@^7.7.0":
  version "7.12.1"
  resolved "http://r.npm.sankuai.com/@babel/polyfill/download/@babel/polyfill-7.12.1.tgz"
  integrity sha1-Hy1jcdEmG72WHzxdWQkVDhLQvZY=
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-env@^7.22.9", "@babel/preset-env@^7.23.6":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.26.0.tgz"
  integrity sha1-MOXGvBvMVIZb/wxaMPbUzNx/qLE=
  dependencies:
    "@babel/compat-data" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.25.9"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.25.9"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.25.9"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.25.9"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.25.9"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.26.0"
    "@babel/plugin-syntax-import-attributes" "^7.26.0"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.25.9"
    "@babel/plugin-transform-async-generator-functions" "^7.25.9"
    "@babel/plugin-transform-async-to-generator" "^7.25.9"
    "@babel/plugin-transform-block-scoped-functions" "^7.25.9"
    "@babel/plugin-transform-block-scoping" "^7.25.9"
    "@babel/plugin-transform-class-properties" "^7.25.9"
    "@babel/plugin-transform-class-static-block" "^7.26.0"
    "@babel/plugin-transform-classes" "^7.25.9"
    "@babel/plugin-transform-computed-properties" "^7.25.9"
    "@babel/plugin-transform-destructuring" "^7.25.9"
    "@babel/plugin-transform-dotall-regex" "^7.25.9"
    "@babel/plugin-transform-duplicate-keys" "^7.25.9"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-dynamic-import" "^7.25.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.25.9"
    "@babel/plugin-transform-export-namespace-from" "^7.25.9"
    "@babel/plugin-transform-for-of" "^7.25.9"
    "@babel/plugin-transform-function-name" "^7.25.9"
    "@babel/plugin-transform-json-strings" "^7.25.9"
    "@babel/plugin-transform-literals" "^7.25.9"
    "@babel/plugin-transform-logical-assignment-operators" "^7.25.9"
    "@babel/plugin-transform-member-expression-literals" "^7.25.9"
    "@babel/plugin-transform-modules-amd" "^7.25.9"
    "@babel/plugin-transform-modules-commonjs" "^7.25.9"
    "@babel/plugin-transform-modules-systemjs" "^7.25.9"
    "@babel/plugin-transform-modules-umd" "^7.25.9"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-new-target" "^7.25.9"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.25.9"
    "@babel/plugin-transform-numeric-separator" "^7.25.9"
    "@babel/plugin-transform-object-rest-spread" "^7.25.9"
    "@babel/plugin-transform-object-super" "^7.25.9"
    "@babel/plugin-transform-optional-catch-binding" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"
    "@babel/plugin-transform-private-methods" "^7.25.9"
    "@babel/plugin-transform-private-property-in-object" "^7.25.9"
    "@babel/plugin-transform-property-literals" "^7.25.9"
    "@babel/plugin-transform-regenerator" "^7.25.9"
    "@babel/plugin-transform-regexp-modifiers" "^7.26.0"
    "@babel/plugin-transform-reserved-words" "^7.25.9"
    "@babel/plugin-transform-shorthand-properties" "^7.25.9"
    "@babel/plugin-transform-spread" "^7.25.9"
    "@babel/plugin-transform-sticky-regex" "^7.25.9"
    "@babel/plugin-transform-template-literals" "^7.25.9"
    "@babel/plugin-transform-typeof-symbol" "^7.25.9"
    "@babel/plugin-transform-unicode-escapes" "^7.25.9"
    "@babel/plugin-transform-unicode-property-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-sets-regex" "^7.25.9"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.38.1"
    semver "^6.3.1"

"@babel/preset-flow@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/preset-flow/download/@babel/preset-flow-7.25.9.tgz"
  integrity sha1-74tefj8kpCs3Eed/sUkZuH3/7Qo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-transform-flow-strip-types" "^7.25.9"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "http://r.npm.sankuai.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.7.4":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/preset-react/download/@babel/preset-react-7.26.3.tgz"
  integrity sha1-fF4CjWI7RoPB+DoL1HE7kQBWDKo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-transform-react-display-name" "^7.25.9"
    "@babel/plugin-transform-react-jsx" "^7.25.9"
    "@babel/plugin-transform-react-jsx-development" "^7.25.9"
    "@babel/plugin-transform-react-pure-annotations" "^7.25.9"

"@babel/runtime-corejs2@^7.18.9":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.26.0.tgz"
  integrity sha1-pJ1omkMuJ7m/6k80+ABqvKjLr7U=
  dependencies:
    core-js "^2.6.12"
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.18.9", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.13", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.26.0.tgz"
  integrity sha1-hgDC9ZXyd8YIFSVkGLhTVqZRc8E=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.25.9.tgz"
  integrity sha1-7LYtgaim9dxf6Kv8OQH8Ut3xUBY=
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.9", "@babel/traverse@^7.26.5", "@babel/traverse@^7.7.4":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.26.5.tgz"
  integrity sha1-bQvj53L/eGRWwaN1OCCChvbnkCE=
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.5"
    "@babel/parser" "^7.26.5"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.5"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.21.3", "@babel/types@^7.25.4", "@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.26.5", "@babel/types@^7.4.4", "@babel/types@^7.7.4":
  version "7.26.5"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.26.5.tgz"
  integrity sha1-eh4cAdKOJtH+f47JVns7krnQd0c=
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.8.0.tgz"
  integrity sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=

"@emotion/unitless@^0.7.5":
  version "0.7.5"
  resolved "http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz"
  integrity sha1-dyESkcGQCnALinjPr9oxYNdpSe0=

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha1-mwQ4T7dxkm36bXrQQyTssqubLig=

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.21.5.tgz"
  integrity sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha1-/G/RGorKVsH284lPK+oEefj2Jrk=

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.1.tgz"
  integrity sha1-0RRb8sIBMtZABJXW30v1k2L9nVY=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/@jest/schemas/download/@jest/schemas-29.6.3.tgz"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz"
  integrity sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/sourcemap-codec@^1.4.13":
  version "1.5.5"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.5.tgz#6912b00d2c631c0d15ce1a7ab57cd657f2a8f8ba"
  integrity sha1-aRKwDSxjHA0Vzhp6tXzWV/Ko+Lo=

"@jridgewell/trace-mapping@^0.3.23", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@ljharb/resumer@~0.0.1":
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/@ljharb/resumer/download/@ljharb/resumer-0.0.1.tgz"
  integrity sha1-ipQKkZLdMfah3xdWS70m3GrT5o0=
  dependencies:
    "@ljharb/through" "^2.3.9"

"@ljharb/through@^2.3.9", "@ljharb/through@~2.3.9":
  version "2.3.13"
  resolved "http://r.npm.sankuai.com/@ljharb/through/download/@ljharb/through-2.3.13.tgz"
  integrity sha1-t+R2bgtlqoLlKb6UWrB43nmHTtw=
  dependencies:
    call-bind "^1.0.7"

"@mfe/bellwether-route@^1.0.9":
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/@mfe/bellwether-route/download/@mfe/bellwether-route-1.0.9.tgz"
  integrity sha1-3Cd9UYvXqp+Aeg0TfKu0SXlJDa0=

"@mfe/cc-api-caller-pc@^0.2.8":
  version "0.2.18"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller-pc/download/@mfe/cc-api-caller-pc-0.2.18.tgz"
  integrity sha1-NHcoUUjo94BeNnjK2RrL5JjfPHc=
  dependencies:
    "@mfe/cc-api-caller" "^0.3.2"

"@mfe/cc-api-caller@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.3.2.tgz"
  integrity sha1-r4VvgTu4FLvDHYeqxzGZsJEz+ho=
  dependencies:
    "@babel/runtime" "^7.18.9"
    ejs "^3.1.6"
    ora "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-ocrm-utils@^0.0.6":
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/@mfe/cc-ocrm-utils/download/@mfe/cc-ocrm-utils-0.0.6.tgz"
  integrity sha1-5EkXkqQipQ1FD2vyiUJb21hntEg=

"@mtfe/sso-web@^2.4.1":
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-web/download/@mtfe/sso-web-2.6.0.tgz"
  integrity sha1-PZ+PWJIQW3CnuoeTS+5Zb2kv/i0=
  dependencies:
    crypto-js "^3.1.9-1"
    eventemitter3 "^5.0.1"
    minimatch "^3.0.4"
    ts-polyfill "^3.0.1"
    whatwg-fetch "^2.0.4"

"@mtfe/yapi2service@^1.1.5":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@mtfe/yapi2service/download/@mtfe/yapi2service-1.2.1.tgz"
  integrity sha1-9vAgQE5jktp3WNPB9U0br0HevKk=
  dependencies:
    axios "^0.24.0"
    chalk "^4.1.2"
    commander "^8.3.0"
    cross-spawn "^7.0.3"
    ejs "3.1.6"
    inquirer "^8.2.0"
    ora "^5.4.1"
    owner "^0.1.0"

"@nicolo-ribaudo/chokidar-2@2.1.8-no-fsevents.3":
  version "2.1.8-no-fsevents.3"
  resolved "http://r.npm.sankuai.com/@nicolo-ribaudo/chokidar-2/download/@nicolo-ribaudo/chokidar-2-2.1.8-no-fsevents.3.tgz"
  integrity sha1-Mj1y3SUQPQxPvc6J2t9XSnh7H5s=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@originjs/vite-plugin-federation@^1.4.1":
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/@originjs/vite-plugin-federation/download/@originjs/vite-plugin-federation-1.4.1.tgz#e6abc8f18f2cf82783eb87853f4d03e6358b43c2"
  integrity sha1-5qvI8Y8s+CeD64eFP00D5jWLQ8I=
  dependencies:
    estree-walker "^3.0.2"
    magic-string "^0.27.0"

"@parcel/watcher-android-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.0.tgz#e32d3dda6647791ee930556aee206fcd5ea0fb7a"
  integrity sha1-4y092mZHeR7pMFVq7iBvzV6g+3o=

"@parcel/watcher-darwin-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.0.tgz"
  integrity sha1-DZ5oC36ewcj1SUTxuUWqh1WvsS8=

"@parcel/watcher-darwin-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.0.tgz#f9f1d5ce9d5878d344f14ef1856b7a830c59d1bb"
  integrity sha1-+fHVzp1YeNNE8U7xhWt6gwxZ0bs=

"@parcel/watcher-freebsd-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.0.tgz#2b77f0c82d19e84ff4c21de6da7f7d096b1a7e82"
  integrity sha1-K3fwyC0Z6E/0wh3m2n99CWsafoI=

"@parcel/watcher-linux-arm-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.0.tgz#92ed322c56dbafa3d2545dcf2803334aee131e42"
  integrity sha1-ku0yLFbbr6PSVF3PKAMzSu4THkI=

"@parcel/watcher-linux-arm-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.0.tgz#cd48e9bfde0cdbbd2ecd9accfc52967e22f849a4"
  integrity sha1-zUjpv94M270uzZrM/FKWfiL4SaQ=

"@parcel/watcher-linux-arm64-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.0.tgz#7b81f6d5a442bb89fbabaf6c13573e94a46feb03"
  integrity sha1-e4H21aRCu4n7q69sE1c+lKRv6wM=

"@parcel/watcher-linux-arm64-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.0.tgz#dcb8ff01077cdf59a18d9e0a4dff7a0cfe5fd732"
  integrity sha1-3Lj/AQd831mhjZ4KTf96DP5f1zI=

"@parcel/watcher-linux-x64-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.0.tgz#2e254600fda4e32d83942384d1106e1eed84494d"
  integrity sha1-LiVGAP2k4y2DlCOE0RBuHu2ESU0=

"@parcel/watcher-linux-x64-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.0.tgz#01fcea60fedbb3225af808d3f0a7b11229792eef"
  integrity sha1-AfzqYP7bsyJa+AjT8KexEil5Lu8=

"@parcel/watcher-win32-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.0.tgz#87cdb16e0783e770197e52fb1dc027bb0c847154"
  integrity sha1-h82xbgeD53AZflL7HcAnuwyEcVQ=

"@parcel/watcher-win32-ia32@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.0.tgz#778c39b56da33e045ba21c678c31a9f9d7c6b220"
  integrity sha1-d4w5tW2jPgRbohxnjDGp+dfGsiA=

"@parcel/watcher-win32-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.0.tgz#33873876d0bbc588aacce38e90d1d7480ce81cb7"
  integrity sha1-M4c4dtC7xYiqzOOOkNHXSAzoHLc=

"@parcel/watcher@^2.4.1":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher/download/@parcel/watcher-2.5.0.tgz"
  integrity sha1-XIiBixK43kMHqdPm3D4o66DfvRA=
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.0"
    "@parcel/watcher-darwin-arm64" "2.5.0"
    "@parcel/watcher-darwin-x64" "2.5.0"
    "@parcel/watcher-freebsd-x64" "2.5.0"
    "@parcel/watcher-linux-arm-glibc" "2.5.0"
    "@parcel/watcher-linux-arm-musl" "2.5.0"
    "@parcel/watcher-linux-arm64-glibc" "2.5.0"
    "@parcel/watcher-linux-arm64-musl" "2.5.0"
    "@parcel/watcher-linux-x64-glibc" "2.5.0"
    "@parcel/watcher-linux-x64-musl" "2.5.0"
    "@parcel/watcher-win32-arm64" "2.5.0"
    "@parcel/watcher-win32-ia32" "2.5.0"
    "@parcel/watcher-win32-x64" "2.5.0"

"@popperjs/core@^2.11.5":
  version "2.11.8"
  resolved "http://r.npm.sankuai.com/@popperjs/core/download/@popperjs/core-2.11.8.tgz"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@rc-component/async-validator@^5.0.3":
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/@rc-component/async-validator/download/@rc-component/async-validator-5.0.4.tgz"
  integrity sha1-UpGtkvAKFLZ2b8gXNcI0J3+D6Ug=
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@rc-component/color-picker/download/@rc-component/color-picker-2.0.1.tgz"
  integrity sha1-a5uWFSRmqdRHXL5ytAtZS/2hZL4=
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    classnames "^2.2.6"
    rc-util "^5.38.1"

"@rc-component/context@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@rc-component/context/download/@rc-component/context-1.4.0.tgz"
  integrity sha1-3G+wIdZ3NUavjwFq5M6a6giDleg=
  dependencies:
    "@babel/runtime" "^7.10.1"
    rc-util "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mini-decimal/download/@rc-component/mini-decimal-1.1.0.tgz"
  integrity sha1-e3o2KxSgpUy1vG/SuCcx8p8R2bA=
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mutate-observer/download/@rc-component/mutate-observer-1.1.0.tgz"
  integrity sha1-7lPMiLeKrePNBlNgkhWkR3k4b9g=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@rc-component/portal/download/@rc-component/portal-1.1.2.tgz"
  integrity sha1-VdseUdeE4DRELpcAU2+qpqtj/HE=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@rc-component/qrcode/download/@rc-component/qrcode-1.0.0.tgz"
  integrity sha1-SKjeXrEdDmWSbxN3xLHvTIiJl/U=
  dependencies:
    "@babel/runtime" "^7.24.7"
    classnames "^2.3.2"
    rc-util "^5.38.0"

"@rc-component/tour@~1.15.1":
  version "1.15.1"
  resolved "http://r.npm.sankuai.com/@rc-component/tour/download/@rc-component/tour-1.15.1.tgz"
  integrity sha1-m3mAglQYX8GelkFy2Z4l6MaADe0=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.2.5", "@rc-component/trigger@^2.2.6":
  version "2.2.6"
  resolved "http://r.npm.sankuai.com/@rc-component/trigger/download/@rc-component/trigger-2.2.6.tgz"
  integrity sha1-v+ZgIxOz+t1llod0ZRH4EymdXqQ=
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    classnames "^2.3.2"
    rc-motion "^2.0.0"
    rc-resize-observer "^1.3.1"
    rc-util "^5.44.0"

"@react-dnd/asap@^5.0.1":
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/@react-dnd/asap/download/@react-dnd/asap-5.0.2.tgz"
  integrity sha1-H4HxJMHNbzlRHBGogc+w9xU0NIg=

"@react-dnd/invariant@^4.0.1":
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/@react-dnd/invariant/download/@react-dnd/invariant-4.0.2.tgz"
  integrity sha1-uS7f/KEKJkZmQzSfrHzfuHmXad8=

"@react-dnd/shallowequal@^4.0.1":
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/@react-dnd/shallowequal/download/@react-dnd/shallowequal-4.0.2.tgz"
  integrity sha1-0bS++kI/aS+kq/HHkglwLn2K5LQ=

"@rollup/plugin-virtual@^3.0.2":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@rollup/plugin-virtual/download/@rollup/plugin-virtual-3.0.2.tgz#17e17eeecb4c9fa1c0a6e72c9e5f66382fddbb82"
  integrity sha1-F+F+7stMn6HApucsnl9mOC/du4I=

"@rollup/pluginutils@^5.1.3":
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-5.1.4.tgz"
  integrity sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-android-arm-eabi@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.30.0.tgz#f2552f6984cfae52784b2fbf0e47633f38955d66"
  integrity sha1-8lUvaYTPrlJ4Sy+/DkdjPziVXWY=

"@rollup/rollup-android-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.30.0.tgz#7e5764268d3049b7341c60f1c650f1d71760a5b2"
  integrity sha1-fldkJo0wSbc0HGDxxlDx1xdgpbI=

"@rollup/rollup-darwin-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.30.0.tgz"
  integrity sha1-ySRVd/ZzgC8PbeDUbud2aR13VS4=

"@rollup/rollup-darwin-x64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.30.0.tgz#e492705339542f8b54fa66f630c9d820bc708693"
  integrity sha1-5JJwUzlUL4tU+mb2MMnYILxwhpM=

"@rollup/rollup-freebsd-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.30.0.tgz#3e13b5d4d44ea87598d5d4db97181db1174fb3c8"
  integrity sha1-PhO11NROqHWY1dTblxgdsRdPs8g=

"@rollup/rollup-freebsd-x64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.30.0.tgz#138daa08d1b345d605f57b4dedd18a50420488e7"
  integrity sha1-E42qCNGzRdYF9XtN7dGKUEIEiOc=

"@rollup/rollup-linux-arm-gnueabihf@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.30.0.tgz#bdaece34f93c3dfd521e9ab8f5c740121862468e"
  integrity sha1-va7ONPk8Pf1SHpq49cdAEhhiRo4=

"@rollup/rollup-linux-arm-musleabihf@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.30.0.tgz#1804c6ec49be21521eac612513e0666cdde2188c"
  integrity sha1-GATG7Em+IVIerGElE+BmbN3iGIw=

"@rollup/rollup-linux-arm64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.30.0.tgz#2c4bd90f77fcf769502743ec38f184c00a087e08"
  integrity sha1-LEvZD3f892lQJ0PsOPGEwAoIfgg=

"@rollup/rollup-linux-arm64-musl@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.30.0.tgz#63eadee20f220d28e85cbd10aba671ada8e89c84"
  integrity sha1-Y+re4g8iDSjoXL0Qq6ZxrajonIQ=

"@rollup/rollup-linux-loongarch64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.30.0.tgz#1c2c2bb30f61cbbc0fcf4e6c359777fcdb7108cc"
  integrity sha1-HCwrsw9hy7wPz05sNZd3/NtxCMw=

"@rollup/rollup-linux-powerpc64le-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.30.0.tgz#cea71e0359f086a01c57cf312bef9ec9cc3ba010"
  integrity sha1-zqceA1nwhqAcV88xK++eycw7oBA=

"@rollup/rollup-linux-riscv64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.30.0.tgz#25ab4a6dbcbd27f4a68382f7963363f886a237aa"
  integrity sha1-JatKbby9J/Smg4L3ljNj+IaiN6o=

"@rollup/rollup-linux-s390x-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.30.0.tgz#7054b237152d9e36c51194532a6b70ca1a62a487"
  integrity sha1-cFSyNxUtnjbFEZRTKmtwyhpipIc=

"@rollup/rollup-linux-x64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.30.0.tgz#3656a8341a6048f2111f423301aaad8e84a5fe90"
  integrity sha1-NlaoNBpgSPIRH0IzAaqtjoSl/pA=

"@rollup/rollup-linux-x64-musl@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.30.0.tgz#cf8ae018ea6ff65eb36722a28beb93a20a6047f0"
  integrity sha1-z4rgGOpv9l6zZyKii+uTogpgR/A=

"@rollup/rollup-win32-arm64-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.30.0.tgz#6b968f5b068469db16eac743811ee6c040671042"
  integrity sha1-a5aPWwaEadsW6sdDgR7mwEBnEEI=

"@rollup/rollup-win32-ia32-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.30.0.tgz#0321de1a0540dd402e8e523d90cbd9d16f1b9e96"
  integrity sha1-AyHeGgVA3UAujlI9kMvZ0W8bnpY=

"@rollup/rollup-win32-x64-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.30.0.tgz#7384b359bb45c0c3c76ba2c7aaec1d047305efcb"
  integrity sha1-c4SzWbtFwMPHa6LHquwdBHMF78s=

"@roo/analyze@^0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@roo/analyze/download/@roo/analyze-0.0.7.tgz"
  integrity sha1-Wiqo7i0XdmqQbhpfyISXwNaxxno=
  dependencies:
    "@babel/cli" "^7.8.0"
    "@babel/core" "^7.7.5"
    "@babel/generator" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/plugin-proposal-decorators" "^7.7.4"
    "@babel/plugin-proposal-export-default-from" "^7.7.4"
    "@babel/plugin-syntax-decorators" "^7.7.4"
    "@babel/plugin-syntax-dynamic-import" "^7.7.4"
    "@babel/plugin-transform-destructuring" "^7.7.4"
    "@babel/polyfill" "^7.7.0"
    "@babel/preset-flow" "^7.7.4"
    "@babel/preset-react" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"
    dayjs "^1.9.1"
    lodash "^4.17.15"
    minimist "^1.2.5"
    moment "^2.24.0"

"@roo/create-react-ref@0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@roo/create-react-ref/download/@roo/create-react-ref-0.0.2.tgz"
  integrity sha1-v3SgzB3j3Kkbq5jsIQS58IvxyRk=

"@roo/react-color@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@roo/react-color/download/@roo/react-color-1.0.2.tgz"
  integrity sha1-GavorfhOOE5XZq8HH68L//0EVVk=
  dependencies:
    lodash "^4.17.11"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

"@roo/roo-cooperation-report@^0.0.9":
  version "0.0.9"
  resolved "http://r.npm.sankuai.com/@roo/roo-cooperation-report/download/@roo/roo-cooperation-report-0.0.9.tgz"
  integrity sha1-Pb30HVvtN473GgV9/8BdjRVVDGE=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.22.9"
    "@babel/preset-env" "^7.22.9"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus-report@0.0.3":
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus-report/download/@roo/roo-plus-report-0.0.3.tgz"
  integrity sha1-vKfIPAJrZEM0Gb63NGGVxUgwdnY=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.23.6"
    "@babel/preset-env" "^7.23.6"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus@^0.4.1-beta.2":
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus/download/@roo/roo-plus-0.4.4.tgz"
  integrity sha1-eg/7WnBL8HVI6WOtRtWFQ6Ocq38=
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@roo/roo-plus-report" "0.0.3"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@wangeditor/editor" "^5.1.23"
    "@wangeditor/editor-for-react" "^1.0.6"
    "@yyfe/Copy" "^1.0.15"
    axios "^0.19.0"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    create-react-context "^0.3.0"
    cropperjs "^1.5.13"
    immer "^9.0.17"
    immutability-helper "^3.0.1"
    lodash "^4.17.21"
    qs "^6.11.0"
    quill-image-resize-module-react "^3.0.0"
    rc-progress "^3.5.1"
    react-load-script "0.0.6"
    react-quill "^2.0.0"
    react-router "^4.3.1"
    react-router-dom "^4.3.1"
    uuid "^9.0.0"
    warning "^4.0.3"

"@roo/roo-report@0.0.14":
  version "0.0.14"
  resolved "http://r.npm.sankuai.com/@roo/roo-report/download/@roo/roo-report-0.0.14.tgz"
  integrity sha1-dEECGwPMvvIMkMzfcR1NfKje/G8=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.23.6"
    "@babel/preset-env" "^7.23.6"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-theme-var@^1.4.5":
  version "1.4.5"
  resolved "http://r.npm.sankuai.com/@roo/roo-theme-var/download/@roo/roo-theme-var-1.4.5.tgz"
  integrity sha1-8h0ga7IHzVRWPa4IJxEMVoPqBeU=

"@roo/roo@^1.15.1-beta.2":
  version "1.17.26"
  resolved "http://r.npm.sankuai.com/@roo/roo/download/@roo/roo-1.17.26.tgz"
  integrity sha1-BY23NaN+xxscEeLy+ZIhUA4SZyM=
  dependencies:
    "@ai/mss-upload-js" "^1.1.7"
    "@babel/runtime-corejs2" "^7.18.9"
    "@popperjs/core" "^2.11.5"
    "@rc-component/trigger" "^2.2.5"
    "@roo/create-react-ref" "0.0.2"
    "@roo/react-color" "^1.0.2"
    "@roo/roo-report" "0.0.14"
    "@roo/roo-theme-var" "^1.4.5"
    "@thh/roo-motion" "1.0.0"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/cookie" "^0.1.6"
    "@utiljs/dom" "^0.2.6"
    "@utiljs/functional" "^0.6.5"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@utiljs/param" "^0.6.11"
    "@utiljs/type" "^0.5.5"
    "@wangeditor/editor" "^5.1.23"
    "@wangeditor/editor-for-react" "^1.0.6"
    "@yyfe/Copy" "^1.0.21"
    async-validator "^1.10.0"
    axios "^0.18.0"
    classnames "^2.2.6"
    create-react-context "^0.3.0"
    cropperjs "^1.5.13"
    dayjs "1.11.13"
    dom-lib "^1.2.1"
    eslint-config-prettier "^6.15.0"
    eslint-plugin-prettier "^3.4.1"
    hoist-non-react-statics "^3.3.1"
    identity-obj-proxy "3.0.0"
    immutability-helper "^3.1.1"
    lodash "^4.17.15"
    lodash.omit "^4.5.0"
    memoize-one "^5.1.1"
    moment "^2.29.4"
    object-path "^0.11.4"
    prop-types "^15.8.1"
    rc-field-form "~1.38.2"
    rc-menu "~9.16.0"
    rc-progress "~3.2.1"
    rc-table "~7.27.2"
    rc-util "^5.32.2"
    rc-virtual-list "^3.14.2"
    react-beautiful-dnd "13.0.0"
    react-click-outside "^3.0.1"
    react-dnd "^16.0.1"
    react-dnd-html5-backend "^16.0.1"
    react-drag-listview "^2.0.0"
    react-fast-compare "^2.0.4"
    react-fast-marquee "^1.2.1"
    react-is "^18.2.0"
    react-lifecycles-compat "^3.0.4"
    react-popper "^2.3.0"
    react-resize-detector "^4.2.1"
    react-slick "^0.30.2"
    react-transition-group "^2.5.3"
    react-window "^1.8.8"
    reqwest "^2.0.5"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.31"
    warning "^4.0.3"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "http://r.npm.sankuai.com/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@svgr/babel-plugin-add-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-add-jsx-attribute/download/@svgr/babel-plugin-add-jsx-attribute-8.0.0.tgz"
  integrity sha1-QAH11d2H+hMwPjbuEG4/86friyI=

"@svgr/babel-plugin-remove-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-attribute/download/@svgr/babel-plugin-remove-jsx-attribute-8.0.0.tgz"
  integrity sha1-aRd/eTcjPKyjoa+wUZBmmPL1kYY=

"@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-empty-expression/download/@svgr/babel-plugin-remove-jsx-empty-expression-8.0.0.tgz"
  integrity sha1-wsSBBM/X3NVX83O3Clbp472uHUQ=

"@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-replace-jsx-attribute-value/download/@svgr/babel-plugin-replace-jsx-attribute-value-8.0.0.tgz"
  integrity sha1-j7trLpH6JqxdSqJca25PIPnAric=

"@svgr/babel-plugin-svg-dynamic-title@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-dynamic-title/download/@svgr/babel-plugin-svg-dynamic-title-8.0.0.tgz"
  integrity sha1-HVuh0oE2P8Dy8ppg1tk2+bvGV7A=

"@svgr/babel-plugin-svg-em-dimensions@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-em-dimensions/download/@svgr/babel-plugin-svg-em-dimensions-8.0.0.tgz"
  integrity sha1-NeCN8wDqix1By49iMJwkGwNp5QE=

"@svgr/babel-plugin-transform-react-native-svg@8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-react-native-svg/download/@svgr/babel-plugin-transform-react-native-svg-8.1.0.tgz"
  integrity sha1-kKi2OZi2iLKE8lXGpSSKvVso11Q=

"@svgr/babel-plugin-transform-svg-component@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-svg-component/download/@svgr/babel-plugin-transform-svg-component-8.0.0.tgz"
  integrity sha1-ATtL/KiHeXEfDtJznz9+/O/PT34=

"@svgr/babel-preset@8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-preset/download/@svgr/babel-preset-8.1.0.tgz"
  integrity sha1-DocRmuzfHEJIQLnUVltxN8q/ns4=
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title" "8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions" "8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg" "8.1.0"
    "@svgr/babel-plugin-transform-svg-component" "8.0.0"

"@svgr/core@^8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/core/download/@svgr/core-8.1.0.tgz"
  integrity sha1-QRRvm0CxoQvq9cxPNhoWo8GIXog=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    camelcase "^6.2.0"
    cosmiconfig "^8.1.3"
    snake-case "^3.0.4"

"@svgr/hast-util-to-babel-ast@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/hast-util-to-babel-ast/download/@svgr/hast-util-to-babel-ast-8.0.0.tgz"
  integrity sha1-aVL9nOD0cOGt7Sk7eSonBfr0/9Q=
  dependencies:
    "@babel/types" "^7.21.3"
    entities "^4.4.0"

"@svgr/plugin-jsx@^8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/plugin-jsx/download/@svgr/plugin-jsx-8.1.0.tgz"
  integrity sha1-lpafBKJLWLF07kzZdMYEday9aSg=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    "@svgr/hast-util-to-babel-ast" "8.0.0"
    svg-parser "^2.0.4"

"@swc/core-darwin-arm64@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-arm64/download/@swc/core-darwin-arm64-1.10.7.tgz"
  integrity sha1-/3J95h+qv73+BidH5HMF7jRyKY4=

"@swc/core-darwin-arm64@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-arm64/download/@swc/core-darwin-arm64-1.13.3.tgz#aaab6af81f255bdc9d3bf1d8d38457236cab1a02"
  integrity sha1-qqtq+B8lW9ydO/HY04RXI2yrGgI=

"@swc/core-darwin-x64@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-x64/download/@swc/core-darwin-x64-1.10.7.tgz#a276d5ee56e7c9fb03201c92c620143f8df6b52e"
  integrity sha1-onbV7lbnyfsDIBySxiAUP432tS4=

"@swc/core-darwin-x64@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-x64/download/@swc/core-darwin-x64-1.13.3.tgz#2f65063a9ffb169eec810d2d063d93d21b8ec593"
  integrity sha1-L2UGOp/7Fp7sgQ0tBj2T0huOxZM=

"@swc/core-linux-arm-gnueabihf@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm-gnueabihf/download/@swc/core-linux-arm-gnueabihf-1.10.7.tgz#8f2041b818691e7535bc275d32659e77b5f2fecc"
  integrity sha1-jyBBuBhpHnU1vCddMmWed7Xy/sw=

"@swc/core-linux-arm-gnueabihf@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm-gnueabihf/download/@swc/core-linux-arm-gnueabihf-1.13.3.tgz#1e4823f031f8ed8d77b0ea8ed70130cda2da6f1e"
  integrity sha1-Hkgj8DH47Y13sOqO1wEwzaLabx4=

"@swc/core-linux-arm64-gnu@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-gnu/download/@swc/core-linux-arm64-gnu-1.10.7.tgz#c185499f7db12ee95fdceb4c00fb503ed398cf1d"
  integrity sha1-wYVJn32xLulf3OtMAPtQPtOYzx0=

"@swc/core-linux-arm64-gnu@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-gnu/download/@swc/core-linux-arm64-gnu-1.13.3.tgz#1a82f884e9a73c5fb80a94ec67ee98e255f93cdd"
  integrity sha1-GoL4hOmnPF+4CpTsZ+6Y4lX5PN0=

"@swc/core-linux-arm64-musl@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-musl/download/@swc/core-linux-arm64-musl-1.10.7.tgz#20732c402ba44fbd708e9871aaa10df5597a3d01"
  integrity sha1-IHMsQCukT71wjphxqqEN9Vl6PQE=

"@swc/core-linux-arm64-musl@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-musl/download/@swc/core-linux-arm64-musl-1.13.3.tgz#f556489bec2451b8a3f28239e115a9480421c008"
  integrity sha1-9VZIm+wkUbij8oI54RWpSAQhwAg=

"@swc/core-linux-x64-gnu@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-gnu/download/@swc/core-linux-x64-gnu-1.10.7.tgz#d6310152dd154c0796d1c0d99eb89fc26957c8f6"
  integrity sha1-1jEBUt0VTAeW0cDZnrifwmlXyPY=

"@swc/core-linux-x64-gnu@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-gnu/download/@swc/core-linux-x64-gnu-1.13.3.tgz#29e78da291a6ac800e807771a40f6a41d18f0ead"
  integrity sha1-KeeNopGmrIAOgHdxpA9qQdGPDq0=

"@swc/core-linux-x64-musl@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-musl/download/@swc/core-linux-x64-musl-1.10.7.tgz#e03d4ec66f4234323887774151d1034339d0d7af"
  integrity sha1-4D1Oxm9CNDI4h3dBUdEDQznQ168=

"@swc/core-linux-x64-musl@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-musl/download/@swc/core-linux-x64-musl-1.13.3.tgz#5f2b0639f54f89468ad2e464ba6b45ce19adeca2"
  integrity sha1-XysGOfVPiUaK0uRkumtFzhmt7KI=

"@swc/core-win32-arm64-msvc@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-arm64-msvc/download/@swc/core-win32-arm64-msvc-1.10.7.tgz#f1a8c3149e2671d477af4ca39c761d6ade342d4c"
  integrity sha1-8ajDFJ4mcdR3r0yjnHYdat40LUw=

"@swc/core-win32-arm64-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-arm64-msvc/download/@swc/core-win32-arm64-msvc-1.13.3.tgz#911185c11158b29a8884aea7036115a814a3725a"
  integrity sha1-kRGFwRFYspqIhK6nA2EVqBSjclo=

"@swc/core-win32-ia32-msvc@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-ia32-msvc/download/@swc/core-win32-ia32-msvc-1.10.7.tgz#133f3168fee9910566a874eb1d422dc79eb17d54"
  integrity sha1-Ez8xaP7pkQVmqHTrHUItx56xfVQ=

"@swc/core-win32-ia32-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-ia32-msvc/download/@swc/core-win32-ia32-msvc-1.13.3.tgz#279044bfdba0853f1afd138f582952461544e8e8"
  integrity sha1-J5BEv9ughT8a/ROPWClSRhVE6Og=

"@swc/core-win32-x64-msvc@1.10.7":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-x64-msvc/download/@swc/core-win32-x64-msvc-1.10.7.tgz#84d6ed82b2f19bc00b868c9747f03ea6661d8023"
  integrity sha1-hNbtgrLxm8ALhoyXR/A+pmYdgCM=

"@swc/core-win32-x64-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-x64-msvc/download/@swc/core-win32-x64-msvc-1.13.3.tgz#6069e132be45ac34ecb4d72730db53c60d6a5475"
  integrity sha1-YGnhMr5FrDTstNcnMNtTxg1qVHU=

"@swc/core@^1.10.16":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core/download/@swc/core-1.13.3.tgz#7a8668d96a28b3431acc3b9652f2d3ff2b6e5531"
  integrity sha1-eoZo2Woos0MazDuWUvLT/ytuVTE=
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.23"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.13.3"
    "@swc/core-darwin-x64" "1.13.3"
    "@swc/core-linux-arm-gnueabihf" "1.13.3"
    "@swc/core-linux-arm64-gnu" "1.13.3"
    "@swc/core-linux-arm64-musl" "1.13.3"
    "@swc/core-linux-x64-gnu" "1.13.3"
    "@swc/core-linux-x64-musl" "1.13.3"
    "@swc/core-win32-arm64-msvc" "1.13.3"
    "@swc/core-win32-ia32-msvc" "1.13.3"
    "@swc/core-win32-x64-msvc" "1.13.3"

"@swc/core@^1.7.26":
  version "1.10.7"
  resolved "http://r.npm.sankuai.com/@swc/core/download/@swc/core-1.10.7.tgz"
  integrity sha1-c2pbvw23Yoyy3j6shx4zH5on5gs=
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.17"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.10.7"
    "@swc/core-darwin-x64" "1.10.7"
    "@swc/core-linux-arm-gnueabihf" "1.10.7"
    "@swc/core-linux-arm64-gnu" "1.10.7"
    "@swc/core-linux-arm64-musl" "1.10.7"
    "@swc/core-linux-x64-gnu" "1.10.7"
    "@swc/core-linux-x64-musl" "1.10.7"
    "@swc/core-win32-arm64-msvc" "1.10.7"
    "@swc/core-win32-ia32-msvc" "1.10.7"
    "@swc/core-win32-x64-msvc" "1.10.7"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@swc/counter/download/@swc/counter-0.1.3.tgz"
  integrity sha1-zHRjvQKUlhHGMpWW/M0rDseCsOk=

"@swc/types@^0.1.17":
  version "0.1.17"
  resolved "http://r.npm.sankuai.com/@swc/types/download/@swc/types-0.1.17.tgz"
  integrity sha1-vR2U5zSX8nNBvxQavfTIUjDUHnw=
  dependencies:
    "@swc/counter" "^0.1.3"

"@swc/types@^0.1.23":
  version "0.1.24"
  resolved "http://r.npm.sankuai.com/@swc/types/download/@swc/types-0.1.24.tgz#00f4343e2c966eac178cde89e8d821a784f7586d"
  integrity sha1-APQ0PiyWbqwXjN6J6Nghp4T3WG0=
  dependencies:
    "@swc/counter" "^0.1.3"

"@testing-library/dom@^10.0.0":
  version "10.4.0"
  resolved "http://r.npm.sankuai.com/@testing-library/dom/download/@testing-library/dom-10.4.0.tgz"
  integrity sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.3.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/react@^15.0.2":
  version "15.0.7"
  resolved "http://r.npm.sankuai.com/@testing-library/react/download/@testing-library/react-15.0.7.tgz"
  integrity sha1-/3M84Ik8h1y1pHZy6OdyiXEo9K4=
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@testing-library/dom" "^10.0.0"
    "@types/react-dom" "^18.0.0"

"@thh/roo-motion@1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@thh/roo-motion/download/@thh/roo-motion-1.0.0.tgz"
  integrity sha1-IUw/0JYCPw7Ignzc+2mEG6HEV80=
  dependencies:
    react "^16.12.0 || ^17.0.0"

"@transloadit/prettier-bytes@0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@transloadit/prettier-bytes/download/@transloadit/prettier-bytes-0.0.7.tgz"
  integrity sha1-zbU5n0Rf3WBu2DOHL6DKvbxRaGs=

"@types/aria-query@^5.0.1":
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/@types/aria-query/download/@types/aria-query-5.0.4.tgz"
  integrity sha1-GjHD03iFDSd42rtjdNA23LpLpwg=

"@types/d3-timer@^2.0.0":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@types/d3-timer/download/@types/d3-timer-2.0.3.tgz"
  integrity sha1-10NQqetZkfBUss+Oku+vIr4+GiU=

"@types/debug@^4.0.0":
  version "4.1.12"
  resolved "http://r.npm.sankuai.com/@types/debug/download/@types/debug-4.1.12.tgz"
  integrity sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=
  dependencies:
    "@types/ms" "*"

"@types/estree-jsx@^1.0.0":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@types/estree-jsx/download/@types/estree-jsx-1.0.5.tgz"
  integrity sha1-hYqI6iDzT+ZREfAFpon6Hr9w3Bg=
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@1.0.6", "@types/estree@^1.0.0":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.6.tgz"
  integrity sha1-Yo7/7q4gZKG055946B2Ht+X8e1A=

"@types/event-emitter@^0.3.3":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@types/event-emitter/download/@types/event-emitter-0.3.5.tgz"
  integrity sha1-zptRP3LFDc8EQ6EhZak6ebp6cJI=

"@types/file-saver@^2.0.1":
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/@types/file-saver/download/@types/file-saver-2.0.7.tgz"
  integrity sha1-jbsvJL3HSGxUqoVOtBSUC70Fb30=

"@types/hast@^3.0.0":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/hast/download/@types/hast-3.0.4.tgz"
  integrity sha1-HWs5mTuCzqateDlFsFCMJZA+Fao=
  dependencies:
    "@types/unist" "*"

"@types/hoist-non-react-statics@^3.3.0":
  version "3.3.6"
  resolved "http://r.npm.sankuai.com/@types/hoist-non-react-statics/download/@types/hoist-non-react-statics-3.3.6.tgz"
  integrity sha1-a7p0ODzauY6NtOIM5bSmuYyu0BA=
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/lodash@^4.17.0":
  version "4.17.14"
  resolved "http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.17.14.tgz"
  integrity sha1-uvwFNTP0zcX8yWNa9GqWPB896v8=

"@types/mdast@^4.0.0":
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/@types/mdast/download/@types/mdast-4.0.4.tgz"
  integrity sha1-fM9y7dLxqn3TQ34YDGQ3NYWATdY=
  dependencies:
    "@types/unist" "*"

"@types/ms@*":
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/@types/ms/download/@types/ms-2.1.0.tgz"
  integrity sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=

"@types/node@*":
  version "22.10.6"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-22.10.6.tgz"
  integrity sha1-XGeV5xY1h2A5+FPLzNWfUj2eQjk=
  dependencies:
    undici-types "~6.20.0"

"@types/node@^20.2.0":
  version "20.17.12"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-20.17.12.tgz"
  integrity sha1-7jt9JaUi/ZVgjBs/ApIcl7k/y9Y=
  dependencies:
    undici-types "~6.19.2"

"@types/prop-types@*":
  version "15.7.14"
  resolved "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.14.tgz"
  integrity sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=

"@types/quill@^1.3.10":
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/@types/quill/download/@types/quill-1.3.10.tgz"
  integrity sha1-3B97ZYf37pS99SkbySKJ9vBJdhM=
  dependencies:
    parchment "^1.1.2"

"@types/react-dom@^18.0.0", "@types/react-dom@^18.0.10":
  version "18.3.5"
  resolved "http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.5.tgz"
  integrity sha1-Rfn4c5jF3OoIW3FcWN3PH69l9xY=

"@types/react-redux@^7.1.20":
  version "7.1.34"
  resolved "http://r.npm.sankuai.com/@types/react-redux/download/@types/react-redux-7.1.34.tgz"
  integrity sha1-g2E+GVfEgVIeZ3a+6sT9UG0RvQ4=
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react@*":
  version "19.0.6"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-19.0.6.tgz"
  integrity sha1-mN6uTFxLJHNeXZ40EwL569RegNM=
  dependencies:
    csstype "^3.0.2"

"@types/react@^18.0.27":
  version "18.3.18"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.18.tgz"
  integrity sha1-mzgsTNMuE+Rj+X3wfC7ju80mkEs=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/semver@^7.3.12":
  version "7.5.8"
  resolved "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.5.8.tgz"
  integrity sha1-gmioxXo+Sr0lwWXs02I323lIpV4=

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/@types/unist/download/@types/unist-3.0.3.tgz"
  integrity sha1-rKqw+RnOaczmKcLU7S60rcG2wgw=

"@types/unist@^2.0.0":
  version "2.0.11"
  resolved "http://r.npm.sankuai.com/@types/unist/download/@types/unist-2.0.11.tgz"
  integrity sha1-Ea9XsSfjJId3SEH3pOVOqxZtA8Q=

"@typescript-eslint/eslint-plugin@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz"
  integrity sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz"
  integrity sha1-G2PQgthJovyuilaSSPvi7huKVsc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz"
  integrity sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz"
  integrity sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz"
  integrity sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz"
  integrity sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz"
  integrity sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz"
  integrity sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.0.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@ungap/structured-clone@^1.2.0":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.2.1.tgz"
  integrity sha1-KPoYX2far3t6GowdRFEyxdl5+L0=

"@uppy/companion-client@^2.2.2":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@uppy/companion-client/download/@uppy/companion-client-2.2.2.tgz"
  integrity sha1-xwtC/cynKO+Is+6/fuPi+gS0kjs=
  dependencies:
    "@uppy/utils" "^4.1.2"
    namespace-emitter "^2.0.1"

"@uppy/core@^2.1.1":
  version "2.3.4"
  resolved "http://r.npm.sankuai.com/@uppy/core/download/@uppy/core-2.3.4.tgz"
  integrity sha1-JguFtr86oDzcZ9ojH4xpz7/cyEo=
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    lodash.throttle "^4.1.1"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^3.1.25"
    preact "^10.5.13"

"@uppy/store-default@^2.1.1":
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/@uppy/store-default/download/@uppy/store-default-2.1.1.tgz"
  integrity sha1-YqZWoJm9qgEjBuBU0JN1TLLTbj4=

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/utils/download/@uppy/utils-4.1.3.tgz"
  integrity sha1-nQvm7OTfJfIo0w70C+DxQggljOM=
  dependencies:
    lodash.throttle "^4.1.1"

"@uppy/xhr-upload@^2.0.3":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/xhr-upload/download/@uppy/xhr-upload-2.1.3.tgz"
  integrity sha1-DU41UzL+DG6zctdzExXgTQKu6xg=
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    nanoid "^3.1.25"

"@utiljs/clone@^0.2.8":
  version "0.2.8"
  resolved "http://r.npm.sankuai.com/@utiljs/clone/download/@utiljs/clone-0.2.8.tgz"
  integrity sha1-y3Pbuc5gsyVv7nZLlc9WUba1x60=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/console@0.1.5":
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz"
  integrity sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY=

"@utiljs/console@0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.6.tgz"
  integrity sha1-kPFQx0kSH0DsNj7qxYVgXklIisc=

"@utiljs/cookie@^0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/cookie/download/@utiljs/cookie-0.1.6.tgz"
  integrity sha1-y9CLSVGW3/bApk2vckWQyo3fO/U=

"@utiljs/dom@^0.2.6":
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/@utiljs/dom/download/@utiljs/dom-0.2.6.tgz"
  integrity sha1-gmmFL3g49fBfyRn66lbwvouuRHE=
  dependencies:
    "@utiljs/console" "0.1.6"
    "@utiljs/is" "0.11.10"

"@utiljs/extend@0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz"
  integrity sha1-b1mA5noquNiV3FlQOKe7jas5M3o=
  dependencies:
    "@utiljs/is" "0.11.10"

"@utiljs/functional@^0.6.5":
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/@utiljs/functional/download/@utiljs/functional-0.6.5.tgz"
  integrity sha1-vIlrkuoCNQq9QyPdyIldemdLEXE=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/guid@^0.5.7":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@utiljs/guid/download/@utiljs/guid-0.5.7.tgz"
  integrity sha1-vSw0HPpJAGKrl9CPUzRodZtspuk=

"@utiljs/is@0.11.10", "@utiljs/is@^0.11.10":
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz"
  integrity sha1-OybUJruQeMa/2OrPmrp81tbhcW4=
  dependencies:
    "@utiljs/string" "0.6.6"
    "@utiljs/type" "0.5.5"

"@utiljs/param@^0.6.11":
  version "0.6.11"
  resolved "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz"
  integrity sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA=
  dependencies:
    "@utiljs/extend" "0.1.9"
    "@utiljs/type" "0.5.5"

"@utiljs/string@0.6.6":
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz"
  integrity sha1-YoVx7FwKwSux6jUmWG+e7XdMm38=
  dependencies:
    "@utiljs/console" "0.1.5"
    "@utiljs/type" "0.5.4"

"@utiljs/type@0.5.4":
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz"
  integrity sha1-ljgZeJK11DoYV7vBxlB8DdANGMY=

"@utiljs/type@0.5.5", "@utiljs/type@^0.5.5":
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz"
  integrity sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw=

"@vitejs/plugin-react-swc@^3.2.0":
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-react-swc/download/@vitejs/plugin-react-swc-3.7.2.tgz"
  integrity sha1-sJWN1ExI29N7XviHvbi40SdvJM0=
  dependencies:
    "@swc/core" "^1.7.26"

"@vitest/coverage-v8@1":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/coverage-v8/download/@vitest/coverage-v8-1.6.0.tgz"
  integrity sha1-L1TM9MLZ8jpxKUq6f5Wz0uJ9FOc=
  dependencies:
    "@ampproject/remapping" "^2.2.1"
    "@bcoe/v8-coverage" "^0.2.3"
    debug "^4.3.4"
    istanbul-lib-coverage "^3.2.2"
    istanbul-lib-report "^3.0.1"
    istanbul-lib-source-maps "^5.0.4"
    istanbul-reports "^3.1.6"
    magic-string "^0.30.5"
    magicast "^0.3.3"
    picocolors "^1.0.0"
    std-env "^3.5.0"
    strip-literal "^2.0.0"
    test-exclude "^6.0.0"

"@vitest/expect@1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/expect/download/@vitest/expect-1.6.0.tgz"
  integrity sha1-CzugkU9zhQhGSYP02BG8EitR+zA=
  dependencies:
    "@vitest/spy" "1.6.0"
    "@vitest/utils" "1.6.0"
    chai "^4.3.10"

"@vitest/runner@1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/runner/download/@vitest/runner-1.6.0.tgz"
  integrity sha1-pt5JqWyzOw47oNkGSj6NbOLwiCU=
  dependencies:
    "@vitest/utils" "1.6.0"
    p-limit "^5.0.0"
    pathe "^1.1.1"

"@vitest/snapshot@1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/snapshot/download/@vitest/snapshot-1.6.0.tgz"
  integrity sha1-3rfkSYpSmcEZgTb1bm4PaS5q9HA=
  dependencies:
    magic-string "^0.30.5"
    pathe "^1.1.1"
    pretty-format "^29.7.0"

"@vitest/spy@1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/spy/download/@vitest/spy-1.6.0.tgz"
  integrity sha1-Niy9QszbA/FhN5j96ZeZZJUWkG0=
  dependencies:
    tinyspy "^2.2.0"

"@vitest/utils@1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@vitest/utils/download/@vitest/utils-1.6.0.tgz"
  integrity sha1-XFZ1yn1vVGp7QzfemuiC5sV4lqE=
  dependencies:
    diff-sequences "^29.6.3"
    estree-walker "^3.0.3"
    loupe "^2.3.7"
    pretty-format "^29.7.0"

"@wangeditor/basic-modules@^1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@wangeditor/basic-modules/download/@wangeditor/basic-modules-1.1.7.tgz"
  integrity sha1-qcPM9O9TMy8pVQ1Z02duFfOVlG8=
  dependencies:
    is-url "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@wangeditor/code-highlight/download/@wangeditor/code-highlight-1.0.3.tgz"
  integrity sha1-kCVoV3FNXAz4OsR1rqZNt78pp80=
  dependencies:
    prismjs "^1.23.0"

"@wangeditor/core@^1.1.19":
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/@wangeditor/core/download/@wangeditor/core-1.1.19.tgz"
  integrity sha1-+RVff9ktA8sZgkBbO4LlTDHxwrA=
  dependencies:
    "@types/event-emitter" "^0.3.3"
    event-emitter "^0.3.5"
    html-void-elements "^2.0.0"
    i18next "^20.4.0"
    scroll-into-view-if-needed "^2.2.28"
    slate-history "^0.66.0"

"@wangeditor/editor-for-react@^1.0.6":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor-for-react/download/@wangeditor/editor-for-react-1.0.6.tgz"
  integrity sha1-x3+lZR4Za7flpj5Kvw4y1U1POK8=

"@wangeditor/editor@^5.1.23":
  version "5.1.23"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor/download/@wangeditor/editor-5.1.23.tgz"
  integrity sha1-ydIAe3ywzu9rcmkrTuh7Ae4jZ7M=
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    dom7 "^3.0.0"
    is-hotkey "^0.2.0"
    lodash.camelcase "^4.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"
    lodash.foreach "^4.5.0"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.toarray "^4.4.0"
    nanoid "^3.2.0"
    slate "^0.72.0"
    snabbdom "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@wangeditor/list-module/download/@wangeditor/list-module-1.0.5.tgz"
  integrity sha1-P8CxZ6zd+IVTa0X6DBJ/nGra6jM=

"@wangeditor/table-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/table-module/download/@wangeditor/table-module-1.1.4.tgz"
  integrity sha1-dX1KWGiytlgEHNMjhUpNcHyDR+k=

"@wangeditor/upload-image-module@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@wangeditor/upload-image-module/download/@wangeditor/upload-image-module-1.0.2.tgz"
  integrity sha1-iem5Rn4Qy8axHcV0jgjdI6rr7jA=

"@wangeditor/video-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/video-module/download/@wangeditor/video-module-1.1.4.tgz"
  integrity sha1-ud8bOrLNU/Z4sZtNkn4gB3Sm9TI=

"@yyfe/Copy@^1.0.15", "@yyfe/Copy@^1.0.21":
  version "1.0.23"
  resolved "http://r.npm.sankuai.com/@yyfe/Copy/download/@yyfe/Copy-1.0.23.tgz"
  integrity sha1-QZ1ex77g6Wqd8UPb4mWsr/1FEUg=
  dependencies:
    "@roo/roo-cooperation-report" "^0.0.9"
    copy-html-to-clipboard "^4.0.1"
    copy-image-clipboard "^2.0.1"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.3.2:
  version "8.3.4"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-8.3.4.tgz"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.14.0, acorn@^8.8.2, acorn@^8.9.0:
  version "8.14.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.0.tgz"
  integrity sha1-Bj4scMrF+09kZ/CxEVLgTGgnlbA=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.3"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.3.tgz"
  integrity sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=

ahooks@^3.7.11:
  version "3.8.4"
  resolved "http://r.npm.sankuai.com/ahooks/download/ahooks-3.8.4.tgz"
  integrity sha1-7ioi1Stu5XdDofarUckafDa818Y=
  dependencies:
    "@babel/runtime" "^7.21.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^3.0.5"
    lodash "^4.17.21"
    react-fast-compare "^3.2.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

ajv@^6.12.3, ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/align-text/download/align-text-0.1.4.tgz"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-escapes@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-5.0.0.tgz"
  integrity sha1-tqDK8O7wxBrxkOmnSeDADsBLsqY=
  dependencies:
    type-fest "^1.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.0.0, ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

antd-table-saveas-excel@^2.1.4:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/antd-table-saveas-excel/download/antd-table-saveas-excel-2.1.4.tgz"
  integrity sha1-sMqqKaudTA4hrl4WsUOH48cPeMc=
  dependencies:
    "@types/file-saver" "^2.0.1"
    better-xlsx "^0.7.5"
    file-saver "^2.0.2"

antd@^5.15.4:
  version "5.23.1"
  resolved "http://r.npm.sankuai.com/antd/download/antd-5.23.1.tgz"
  integrity sha1-KFFrvOa93Hth+8zT0aewp3LFS3M=
  dependencies:
    "@ant-design/colors" "^7.2.0"
    "@ant-design/cssinjs" "^1.22.0"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.5.2"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.26.0"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.2.6"
    classnames "^2.5.1"
    copy-to-clipboard "^3.3.3"
    dayjs "^1.11.11"
    rc-cascader "~3.33.0"
    rc-checkbox "~3.5.0"
    rc-collapse "~3.9.0"
    rc-dialog "~9.6.0"
    rc-drawer "~7.2.0"
    rc-dropdown "~4.2.1"
    rc-field-form "~2.7.0"
    rc-image "~7.11.0"
    rc-input "~1.7.2"
    rc-input-number "~9.4.0"
    rc-mentions "~2.19.1"
    rc-menu "~9.16.0"
    rc-motion "^2.9.5"
    rc-notification "~5.6.2"
    rc-pagination "~5.0.0"
    rc-picker "~4.9.2"
    rc-progress "~4.0.0"
    rc-rate "~2.13.0"
    rc-resize-observer "^1.4.3"
    rc-segmented "~2.7.0"
    rc-select "~14.16.5"
    rc-slider "~11.1.8"
    rc-steps "~6.0.1"
    rc-switch "~4.1.0"
    rc-table "~7.50.2"
    rc-tabs "~15.5.0"
    rc-textarea "~1.9.0"
    rc-tooltip "~6.3.2"
    rc-tree "~5.13.0"
    rc-tree-select "~5.27.0"
    rc-upload "~4.8.1"
    rc-util "^5.44.3"
    scroll-into-view-if-needed "^3.1.0"
    throttle-debounce "^5.0.2"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/aria-query/download/aria-query-5.3.0.tgz"
  integrity sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz"
  integrity sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assertion-error@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/assertion-error/download/assertion-error-1.1.0.tgz"
  integrity sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs=

async-validator@^1.10.0:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz"
  integrity sha1-vq5nHnF00pOLe0tp0vt+cit/1yw=

async-validator@^4.1.0:
  version "4.2.5"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-4.2.5.tgz"
  integrity sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=

async@^3.2.3:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz"
  integrity sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=

axios@^0.18.0:
  version "0.18.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.18.1.tgz"
  integrity sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axios@^0.19.0:
  version "0.19.2"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.19.2.tgz"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^0.24.0:
  version "0.24.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.24.0.tgz"
  integrity sha1-gE5voeS5xSiFAd2d/1anoJQNINY=
  dependencies:
    follow-redirects "^1.14.4"

axios@^1.6.8:
  version "1.7.9"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.7.9.tgz"
  integrity sha1-19BxOAwTKiSszaGyz8FTW3nsZQo=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.12"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.12.tgz"
  integrity sha1-ylW77Iqw7e7vPXuP/XUyLiEIeak=
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.6:
  version "0.10.6"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.10.6.tgz"
  integrity sha1-Le2lfK71D1nFJa60lk07L4Z3EMc=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    core-js-compat "^3.38.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.3.tgz"
  integrity sha1-q+sfPxx2LqzjdYf0JUiwi1d4m8g=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"

babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

bail@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/bail/download/bail-2.0.2.tgz"
  integrity sha1-0m9c2P5db4MqMVF7n3w1YEC6bV0=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

better-xlsx@^0.7.5:
  version "0.7.6"
  resolved "http://r.npm.sankuai.com/better-xlsx/download/better-xlsx-0.7.6.tgz"
  integrity sha1-3JvbwwPs23SCP5WCxgjZ9oefS1E=
  dependencies:
    "@babel/runtime" "^7.8.4"
    jszip "^3.2.2"
    kind-of "^6.0.3"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bl@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.24.3:
  version "4.24.4"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.4.tgz"
  integrity sha1-xrKGWj8IvLhgoOgnOJADuf5obks=
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

cac@^6.7.14:
  version "6.7.14"
  resolved "http://r.npm.sankuai.com/cac/download/cac-6.7.14.tgz"
  integrity sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.1.tgz"
  integrity sha1-MuWJLmNhspsLVFum93YzeNrKKEA=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.2, call-bind@^1.0.7, call-bind@^1.0.8, call-bind@~1.0.2:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.3.tgz"
  integrity sha1-Qc/QMrWT45F2pxUzq084SqBP1oE=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^1.0.2:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-1.2.1.tgz"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001688:
  version "1.0.30001692"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001692.tgz"
  integrity sha1-RYVynZXmuVvltDnaarVSUM0SW/k=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

ccount@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/ccount/download/ccount-2.0.1.tgz"
  integrity sha1-F6O/gjAuCHDW2kOgExGovAKj7PU=

center-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/center-align/download/center-align-0.1.3.tgz"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chai@^4.3.10:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/chai/download/chai-4.5.0.tgz"
  integrity sha1-cH5Jkjr92bE6iwtH0z1zLROBL9g=
  dependencies:
    assertion-error "^1.1.0"
    check-error "^1.0.3"
    deep-eql "^4.1.3"
    get-func-name "^2.0.2"
    loupe "^2.3.6"
    pathval "^1.1.1"
    type-detect "^4.1.0"

chalk@5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-5.3.0.tgz"
  integrity sha1-Z8IKfr73Dn85cKAfkPohDLaGA4U=

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-html4@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/character-entities-html4/download/character-entities-html4-2.1.0.tgz"
  integrity sha1-HxrblAyXGksiujndymthjcblays=

character-entities-legacy@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/character-entities-legacy/download/character-entities-legacy-3.0.0.tgz"
  integrity sha1-dryDqQc4kB17wiOp6TdZ/dVgEls=

character-entities@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/character-entities/download/character-entities-2.0.2.tgz"
  integrity sha1-LQnC5yzZUjB2zLIRV9/2atQ/zCI=

character-reference-invalid@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/character-reference-invalid/download/character-reference-invalid-2.0.1.tgz"
  integrity sha1-hcZrBB5DtHIQ+vQBJ4q/gIrEXLk=

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-error@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/check-error/download/check-error-1.0.3.tgz"
  integrity sha1-plAuQxKn7pafZG6Duz3dVigb1pQ=
  dependencies:
    get-func-name "^2.0.2"

cheerio-select@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cheerio-select/download/cheerio-select-2.1.0.tgz"
  integrity sha1-TYZzKGuBJsoqjkJ0DV48SISuIbQ=
  dependencies:
    boolbase "^1.0.0"
    css-select "^5.1.0"
    css-what "^6.1.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"

cheerio@1.0.0-rc.12, cheerio@^1.0.0-rc.10:
  version "1.0.0-rc.12"
  resolved "http://r.npm.sankuai.com/cheerio/download/cheerio-1.0.0-rc.12.tgz#788bf7466506b1c6bf5fae51d24a2c4d62e47683"
  integrity sha1-eIv3RmUGsca/X65R0kosTWLkdoM=
  dependencies:
    cheerio-select "^2.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    htmlparser2 "^8.0.1"
    parse5 "^7.0.0"
    parse5-htmlparser2-tree-adapter "^7.0.0"

chokidar@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-4.0.3.tgz"
  integrity sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=
  dependencies:
    readdirp "^4.0.1"

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1, classnames@^2.3.2, classnames@^2.5.1:
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/classnames/download/classnames-2.5.1.tgz"
  integrity sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-4.0.0.tgz"
  integrity sha1-POz+NzS/T+Aqg2HL3A9v4oxqV+o=
  dependencies:
    restore-cursor "^4.0.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-truncate@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-3.1.0.tgz"
  integrity sha1-PyOrElNePXPoObtD5zyd5IfbE4k=
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-2.1.0.tgz"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.20:
  version "2.0.20"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

comma-separated-tokens@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/comma-separated-tokens/download/comma-separated-tokens-2.0.3.tgz"
  integrity sha1-TonJRYrLYbyP7xn0UplzsjkoOe4=

commander@11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-11.0.0.tgz"
  integrity sha1-Q+GcJdvtyCViA1OOjX6TRod6b2c=

commander@^2.20.0:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^6.2.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-6.2.1.tgz"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-1.0.20.tgz"
  integrity sha1-F2i1Ui0RcnVPXQybAt469r5QakM=

compute-scroll-into-view@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-3.1.0.tgz"
  integrity sha1-dT8R2XJZZVjY/nxry8hJdpCrTIc=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

confbox@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/confbox/download/confbox-0.1.8.tgz"
  integrity sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=

connect-history-api-fallback@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

contour_plot@^0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/contour_plot/download/contour_plot-0.0.1.tgz"
  integrity sha1-R1hw8DK44zhBKqX8UHiA8L9JXHc=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

copy-html-to-clipboard@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/copy-html-to-clipboard/download/copy-html-to-clipboard-4.0.1.tgz"
  integrity sha1-bu2VrVb+Bon0d3LmW3VEODQxl4Y=
  dependencies:
    toggle-selection "^1.0.3"

copy-image-clipboard@^2.0.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/copy-image-clipboard/download/copy-image-clipboard-2.1.2.tgz"
  integrity sha1-eWUDj6oDaxrtRR3yB/FLUoOChqE=

copy-to-clipboard@^3.2.0, copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz"
  integrity sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@^3.38.0, core-js-compat@^3.38.1:
  version "3.40.0"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.40.0.tgz"
  integrity sha1-dIWRKlpKQxXC/bLL3GI+aIHIizg=
  dependencies:
    browserslist "^4.24.3"

core-js@^2.4.0, core-js@^2.6.12, core-js@^2.6.5:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3.6.4:
  version "3.40.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.40.0.tgz"
  integrity sha1-J3P2sGh32O2hAvxC+CgXZDcGJHY=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-8.3.6.tgz"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

create-react-context@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/create-react-context/download/create-react-context-0.3.0.tgz"
  integrity sha1-VG3t6dxCLe8NP8L+A6/gvA9PfYw=
  dependencies:
    gud "^1.0.0"
    warning "^4.0.3"

cropperjs@^1.5.13:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/cropperjs/download/cropperjs-1.6.2.tgz"
  integrity sha1-0aXWJ9iAWBzKQbeQHwaSNQDkIBs=

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^3.1.9-1, crypto-js@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-3.3.0.tgz"
  integrity sha1-hG3RzOL2iqz6FWyFePkmpgm3l2s=

css-box-model@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/css-box-model/download/css-box-model-1.2.1.tgz"
  integrity sha1-WZUdO4H9ayB0pi1JREQVsNK018E=
  dependencies:
    tiny-invariant "^1.0.6"

css-select@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-5.1.0.tgz"
  integrity sha1-uOvWVUw2N8zHZoiAStP2pv2uqKY=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-what@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

cssstyle@4.0.1, cssstyle@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-4.0.1.tgz#ef29c598a1e90125c870525490ea4f354db0660a"
  integrity sha1-7ynFmKHpASXIcFJUkOpPNU2wZgo=
  dependencies:
    rrweb-cssom "^0.6.0"

csstype@^3.0.2, csstype@^3.0.8, csstype@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

"d3-color@1 - 3":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-color/download/d3-color-3.1.0.tgz"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

d3-ease@^1.0.5:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/d3-ease/download/d3-ease-1.0.7.tgz"
  integrity sha1-moNIkO+LiujFWLL+Vb1X9Zk7heI=

d3-hierarchy@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/d3-hierarchy/download/d3-hierarchy-2.0.0.tgz"
  integrity sha1-2riKWMo+ehvGyrOQ6JZn/MbSAhg=

d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-interpolate/download/d3-interpolate-3.0.1.tgz"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-regression@^1.3.5:
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/d3-regression/download/d3-regression-1.3.10.tgz"
  integrity sha1-0aQRq0UETZ6NW4rsBfLlmOGmIck=

d3-timer@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/d3-timer/download/d3-timer-1.0.10.tgz"
  integrity sha1-3+dripF0iDGxO22ceT/71QjdneU=

d@1, d@^1.0.1, d@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/d/download/d-1.0.2.tgz"
  integrity sha1-Ku/VVLgZgefcz3LWhCrnJcsX5d4=
  dependencies:
    es5-ext "^0.10.64"
    type "^2.7.2"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-5.0.0.tgz"
  integrity sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=
  dependencies:
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

dayjs@1.11.13, dayjs@^1.11.11, dayjs@^1.11.13, dayjs@^1.9.1:
  version "1.11.13"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz"
  integrity sha1-kkMLATkFXD67YBUKoT6GCktaNmw=

debug@4, debug@^4.0.0, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.0.tgz"
  integrity sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=
  dependencies:
    ms "^2.1.3"

debug@4.3.4:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debug@=3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.4.3:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.4.3.tgz"
  integrity sha1-EEQJKITSRdG39lcl+krUxveBzCM=

decode-named-character-reference@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/decode-named-character-reference/download/decode-named-character-reference-1.1.0.tgz"
  integrity sha1-XWzmh5KAiQEhDaxCqOmFNRHiuL8=
  dependencies:
    character-entities "^2.0.0"

deep-eql@^4.1.3:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/deep-eql/download/deep-eql-4.1.4.tgz"
  integrity sha1-0NORKGWRG7j6xa+046z6aijccrc=
  dependencies:
    type-detect "^4.0.0"

deep-equal@^1.0.1, deep-equal@~1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/deep-equal/download/deep-equal-1.1.2.tgz"
  integrity sha1-eKVht4MO7zE0x/bzo9avJypnh2E=
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defined@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/defined/download/defined-1.0.1.tgz"
  integrity sha1-wLnbJ7+v/ZXW9hOZQZuJPfD5Hr8=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

dequal@^2.0.0, dequal@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/dequal/download/dequal-2.0.3.tgz"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

detect-browser@^5.0.0, detect-browser@^5.1.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/detect-browser/download/detect-browser-5.3.0.tgz"
  integrity sha1-lwXvK930YHLQ9yZaH+MA42/nzso=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/detect-libc/download/detect-libc-1.0.3.tgz"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

devlop@^1.0.0, devlop@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/devlop/download/devlop-1.1.0.tgz"
  integrity sha1-TbfCyk3G4Og0wwvnDJS7yXbccBg=
  dependencies:
    dequal "^2.0.0"

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-29.6.3.tgz"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dnd-core@^16.0.1:
  version "16.0.1"
  resolved "http://r.npm.sankuai.com/dnd-core/download/dnd-core-16.0.1.tgz"
  integrity sha1-ocIT7QiWH2vRlZoou3bxqGg2DRk=
  dependencies:
    "@react-dnd/asap" "^5.0.1"
    "@react-dnd/invariant" "^4.0.1"
    redux "^4.2.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"
  resolved "http://r.npm.sankuai.com/dom-accessibility-api/download/dom-accessibility-api-0.5.16.tgz"
  integrity sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=

dom-helpers@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/dom-helpers/download/dom-helpers-3.4.0.tgz"
  integrity sha1-6bNpcA+Vn2Ls3lprq95LzNkWmvg=
  dependencies:
    "@babel/runtime" "^7.1.2"

dom-lib@^1.2.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/dom-lib/download/dom-lib-1.3.1.tgz"
  integrity sha1-iFAqlnBSx0QGqiht35nj+8mQUXc=

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-2.0.0.tgz"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom7@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/dom7/download/dom7-3.0.0.tgz"
  integrity sha1-uGHOXWemvs16qjrQKUL/FLEkAzE=
  dependencies:
    ssr-window "^3.0.0-alpha.1"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-5.0.3.tgz"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-3.2.2.tgz"
  integrity sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/dot-case/download/dot-case-3.0.4.tgz"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotignore@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/dotignore/download/dotignore-0.1.2.tgz"
  integrity sha1-+ULyIA0ow6dvvdbw7p8yV8ii6QU=
  dependencies:
    minimatch "^3.0.4"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts-for-react@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/echarts-for-react/download/echarts-for-react-3.0.2.tgz"
  integrity sha1-rFhZFXBIoQZtRVPjSzKKuyTyt8E=
  dependencies:
    fast-deep-equal "^3.1.3"
    size-sensor "^1.0.1"

echarts@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/echarts/download/echarts-6.0.0.tgz"
  integrity sha1-KTWqd1HCgtGru/fXGdOXGZoVuec=
  dependencies:
    tslib "2.3.0"
    zrender "6.0.0"

ejs@3.1.6:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.6.tgz"
  integrity sha1-W/0KBol0O7UmizVQzO7rvBcCgio=
  dependencies:
    jake "^10.6.1"

ejs@^3.1.6:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.10.tgz"
  integrity sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.73:
  version "1.5.80"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.80.tgz"
  integrity sha1-ynqDYdcwXw7J4gPOTmM8u4qO8bE=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/enquire.js/download/enquire.js-2.1.6.tgz"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

entities@^4.2.0, entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9:
  version "1.23.9"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.9.tgz"
  integrity sha1-W0WZS33nja2lwb6/E3lkazK51gY=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz"
  integrity sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.0.1.tgz"
  integrity sha1-7N84tnhLGU04Bl3zJDALv1Fcc+0=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz"
  integrity sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

es5-ext@^0.10.35, es5-ext@^0.10.62, es5-ext@^0.10.64, es5-ext@~0.10.14:
  version "0.10.64"
  resolved "http://r.npm.sankuai.com/es5-ext/download/es5-ext-0.10.64.tgz"
  integrity sha1-EuT/tI8boup3fx/N0ZGO9z6iFxQ=
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    esniff "^2.0.1"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/es6-iterator/download/es6-iterator-2.0.3.tgz"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/es6-symbol/download/es6-symbol-3.1.4.tgz"
  integrity sha1-9OfSgBN3C0II7L8+C/FNO8tVe4w=
  dependencies:
    d "^1.0.2"
    ext "^1.7.0"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.21.5.tgz"
  integrity sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-5.0.0.tgz"
  integrity sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=

eslint-config-prettier@^6.15.0:
  version "6.15.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-config-prettier@^8.7.0:
  version "8.10.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-8.10.0.tgz"
  integrity sha1-OgamYhMIB+JQL8P/i0FD2KBljhE=

eslint-plugin-prettier@^3.4.1:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-prettier@^4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.1.tgz"
  integrity sha1-ZRy7iLHauYv9QvAXoS+mstmT+Us=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react@^7.37.1:
  version "7.37.4"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.37.4.tgz"
  integrity sha1-G2yAthdbauSyYFWuTVXQTEFMcYE=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.8"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-plugin-unused-imports@^4.1.3:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/eslint-plugin-unused-imports/download/eslint-plugin-unused-imports-4.1.4.tgz"
  integrity sha1-Yt3HRGzL+ap7bx8LAKmAQjzaJzg=

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^8.36.0:
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

esniff@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/esniff/download/esniff-2.0.1.tgz"
  integrity sha1-pNS0Olxxx+xRxRCYwdiikIH5swg=
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    event-emitter "^0.3.5"
    type "^2.7.2"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-util-is-identifier-name@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/estree-util-is-identifier-name/download/estree-util-is-identifier-name-3.0.0.tgz"
  integrity sha1-C170xP8TUIs03NAez6lF9h/OXb0=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

estree-walker@^3.0.2, estree-walker@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/event-emitter/download/event-emitter-0.3.5.tgz"
  integrity sha1-***************************=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-2.0.3.tgz"
  integrity sha1-teEHm1n7XhuidxwKmTvgYKWMmbo=

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-5.0.1.tgz"
  integrity sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=

execa@7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-7.2.0.tgz"
  integrity sha1-ZX51uphPQqcPOJKM7ch9by1P5Ok=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

execa@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/execa/download/execa-8.0.1.tgz"
  integrity sha1-UfallDtYD5Y8PKnGMheW24zDm4w=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^8.0.1"
    human-signals "^5.0.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^4.1.0"
    strip-final-newline "^3.0.0"

ext@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/ext/download/ext-1.7.0.tgz"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend@^3.0.0, extend@^3.0.2, extend@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.4.1.tgz"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.1.2.tgz"
  integrity sha1-S2LEK44D3j+EhGC2OQeZIGldAVQ=

fast-diff@^1.1.2, fast-diff@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-xml-parser@^4.2.2:
  version "4.5.1"
  resolved "http://r.npm.sankuai.com/fast-xml-parser/download/fast-xml-parser-4.5.1.tgz"
  integrity sha1-p+Zl/3m3kZEApSAvI5hLYVD5sx4=
  dependencies:
    strnum "^1.0.5"

fastq@^1.6.0:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.18.0.tgz"
  integrity sha1-1jHX4l+v/qgYh/5eqMkBDhs2/uA=
  dependencies:
    reusify "^1.0.4"

fecha@~4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figures@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-saver@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/file-saver/download/file-saver-2.0.5.tgz"
  integrity sha1-1hz+LOBZ9BTYmendbUEH7iVnDDg=

filelist@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.2.tgz"
  integrity sha1-rboUSKmEG+xytCxTLqI9u+3vGic=

fmin@^0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/fmin/download/fmin-0.0.2.tgz"
  integrity sha1-Wbu0DUP/3ByUzQClaMQflfGXMBc=
  dependencies:
    contour_plot "^0.0.1"
    json2module "^0.0.3"
    rollup "^0.25.8"
    tape "^4.5.1"
    uglify-js "^2.6.2"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.5.10.tgz"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.4, follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz"
  integrity sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=

for-each@^0.3.3, for-each@~0.3.3:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.3.tgz"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.1.tgz"
  integrity sha1-uhB22qqlv9fpnBpssCqgpc/5DUg=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

fs-readdir-recursive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fs-readdir-recursive/download/fs-readdir-recursive-1.1.0.tgz"
  integrity sha1-4y/AMKLM7kSmtTcTCNpUvgs5fSc=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-func-name@^2.0.1, get-func-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/get-func-name/download/get-func-name-2.0.2.tgz"
  integrity sha1-DXzyDNE/2oCGaf+oj0/8ejlD/EE=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.7.tgz"
  integrity sha1-3PyzPTJy4V9EXRUSS8CiFhibkEQ=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-stream@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-8.0.1.tgz"
  integrity sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

gl-matrix@^3.0.0, gl-matrix@^3.1.0, gl-matrix@^3.3.0, gl-matrix@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/gl-matrix/download/gl-matrix-3.4.3.tgz"
  integrity sha1-/BGR6DIACf1NIOkzlZXGBB3cIsk=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^7.0.0, glob@^7.1.3, glob@^7.1.4, glob@^7.2.0, glob@~7.2.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.19.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globrex@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/globrex/download/globrex-0.1.2.tgz"
  integrity sha1-3V2eyCYjJzDNZ5Ol4zqTApheYJg=

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

gud@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/gud/download/gud-1.0.0.tgz"
  integrity sha1-pIlYGxfmpwvsqavjrlfeekmYUsA=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

harmony-reflect@^1.4.6:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/harmony-reflect/download/harmony-reflect-1.6.2.tgz"
  integrity sha1-Mey9MuZIo00DDYattn1NR1R/5xA=

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has@~1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/has/download/has-1.0.4.tgz"
  integrity sha1-LrKGDgAAEdrk8UBqhv6A5TD7LsY=

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

hast-util-from-parse5@^8.0.0:
  version "8.0.3"
  resolved "http://r.npm.sankuai.com/hast-util-from-parse5/download/hast-util-from-parse5-8.0.3.tgz"
  integrity sha1-gwo1Ai//KMP+o2l6mML0zGuDWi4=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    devlop "^1.0.0"
    hastscript "^9.0.0"
    property-information "^7.0.0"
    vfile "^6.0.0"
    vfile-location "^5.0.0"
    web-namespaces "^2.0.0"

hast-util-parse-selector@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-parse-selector/download/hast-util-parse-selector-4.0.0.tgz"
  integrity sha1-NSh5+obiVhYDYDfdiTH7XzTLSic=
  dependencies:
    "@types/hast" "^3.0.0"

hast-util-raw@^9.0.0:
  version "9.1.0"
  resolved "http://r.npm.sankuai.com/hast-util-raw/download/hast-util-raw-9.1.0.tgz"
  integrity sha1-ebZrJvb2j7UN+0cWss3KkNkq3y4=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    "@ungap/structured-clone" "^1.0.0"
    hast-util-from-parse5 "^8.0.0"
    hast-util-to-parse5 "^8.0.0"
    html-void-elements "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    parse5 "^7.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"
    web-namespaces "^2.0.0"
    zwitch "^2.0.0"

hast-util-to-jsx-runtime@^2.0.0:
  version "2.3.6"
  resolved "http://r.npm.sankuai.com/hast-util-to-jsx-runtime/download/hast-util-to-jsx-runtime-2.3.6.tgz"
  integrity sha1-/zGJeq5Z9iIy4hWU6sfva2MzPpg=
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    estree-util-is-identifier-name "^3.0.0"
    hast-util-whitespace "^3.0.0"
    mdast-util-mdx-expression "^2.0.0"
    mdast-util-mdx-jsx "^3.0.0"
    mdast-util-mdxjs-esm "^2.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"
    style-to-js "^1.0.0"
    unist-util-position "^5.0.0"
    vfile-message "^4.0.0"

hast-util-to-parse5@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-to-parse5/download/hast-util-to-parse5-8.0.0.tgz"
  integrity sha1-R3zULSeNTwNrwupYWGEw9vOe5u0=
  dependencies:
    "@types/hast" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    property-information "^6.0.0"
    space-separated-tokens "^2.0.0"
    web-namespaces "^2.0.0"
    zwitch "^2.0.0"

hast-util-whitespace@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-whitespace/download/hast-util-whitespace-3.0.0.tgz"
  integrity sha1-d3jtnTyS3Z6MXI9kiknCH8UctiE=
  dependencies:
    "@types/hast" "^3.0.0"

hastscript@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/hastscript/download/hastscript-9.0.1.tgz"
  integrity sha1-28hL72BR1ACENCwinEUc2dxWff8=
  dependencies:
    "@types/hast" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    hast-util-parse-selector "^4.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"

history@^4.7.2:
  version "4.10.1"
  resolved "http://r.npm.sankuai.com/history/download/history-4.10.1.tgz"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hoist-non-react-statics@^2.1.1, hoist-non-react-statics@^2.5.0:
  version "2.5.5"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

html-encoding-sniffer@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-4.0.0.tgz"
  integrity sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=
  dependencies:
    whatwg-encoding "^3.1.1"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-url-attributes@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/html-url-attributes/download/html-url-attributes-3.0.1.tgz"
  integrity sha1-g7BSzV5DcHG3Vs10rnD3CIcMLYc=

html-void-elements@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/html-void-elements/download/html-void-elements-2.0.1.tgz"
  integrity sha1-KUWbiwXCALbF7ph0PEG5edV3VJ8=

html-void-elements@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/html-void-elements/download/html-void-elements-3.0.0.tgz"
  integrity sha1-/J29hK+edHJJA01NYmAt72UX8dc=

htmlparser2@^8.0.1:
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-8.0.2.tgz"
  integrity sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-proxy-agent@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@^7.0.5:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^4.3.0:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-4.3.1.tgz"
  integrity sha1-q3+BHoUfypf/vSwf6alYlk3jIbI=

human-signals@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-5.0.0.tgz"
  integrity sha1-QmZaKE+a4NreO6QevDfrS4UvOig=

husky@^7.0.0:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/husky/download/husky-7.0.4.tgz"
  integrity sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=

i18next@^20.4.0:
  version "20.6.1"
  resolved "http://r.npm.sankuai.com/i18next/download/i18next-20.6.1.tgz"
  integrity sha1-U15fbluutoXH0l33DbY788wKo0U=
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

identity-obj-proxy@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/identity-obj-proxy/download/identity-obj-proxy-3.0.0.tgz"
  integrity sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=
  dependencies:
    harmony-reflect "^1.4.6"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

immediate@~3.0.5:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/immediate/download/immediate-3.0.6.tgz"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

immer@^10.0.2:
  version "10.1.1"
  resolved "http://r.npm.sankuai.com/immer/download/immer-10.1.1.tgz"
  integrity sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=

immer@^9.0.17, immer@^9.0.6:
  version "9.0.21"
  resolved "http://r.npm.sankuai.com/immer/download/immer-9.0.21.tgz"
  integrity sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=

immutability-helper@^3.0.1, immutability-helper@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/immutability-helper/download/immutability-helper-3.1.1.tgz"
  integrity sha1-K4ayKG7TsSQcniO3sh4ERPUvd7c=

immutable@^5.0.2:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-5.0.3.tgz"
  integrity sha1-qgN+IxPqe11ADNkpj6FOQEyTPbE=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inline-style-parser@0.2.4:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/inline-style-parser/download/inline-style-parser-0.2.4.tgz"
  integrity sha1-9K9f5y5hKDn81FPZiaWGVm1pXyI=

inquirer@^8.2.0:
  version "8.2.6"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-8.2.6.tgz"
  integrity sha1-czt0iIGV2NQApnrDMgEbX65epWI=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/interpret/download/interpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.12.2.tgz"
  integrity sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U=

invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

is-alphabetical@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-alphabetical/download/is-alphabetical-2.0.1.tgz"
  integrity sha1-AQcgU+p8EDbfPH0Zptqux/GeeJs=

is-alphanumerical@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-alphanumerical/download/is-alphanumerical-2.0.1.tgz"
  integrity sha1-fAP76W4+kxET5X+WSwo2jMLf2HU=
  dependencies:
    is-alphabetical "^2.0.0"
    is-decimal "^2.0.0"

is-arguments@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.2.0.tgz"
  integrity sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-async-function@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.0.tgz"
  integrity sha1-HRCAYSxJNgjpMWj8RFjCRQdMBqY=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.1.tgz"
  integrity sha1-wg0MZUvgXaT7wjxWJjXAGek9r4k=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.5.tgz"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.3, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-decimal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-decimal/download/is-decimal-2.0.1.tgz"
  integrity sha1-lGnS3BkNAhT9h9eLeMrswMwU7vc=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz"
  integrity sha1-+uMWfHKedGP4RhzlErCApJJoqog=

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-hexadecimal/download/is-hexadecimal-2.0.1.tgz"
  integrity sha1-hrW/Zo/KMHSY0xnfwDKJ14GpACc=

is-hotkey@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/is-hotkey/download/is-hotkey-0.2.0.tgz"
  integrity sha1-GDWmgXGpHlyUYIadljNpR8g0DO8=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-map@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-obj@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-4.1.0.tgz"
  integrity sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-5.0.0.tgz"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-regex@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-stream@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-3.0.0.tgz"
  integrity sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-url@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/is-url/download/is-url-1.2.4.tgz"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.0.tgz"
  integrity sha1-R+NHKulaY/qc8lZgvPDBgcOXcO8=
  dependencies:
    call-bound "^1.0.2"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-report@^3.0.0, istanbul-lib-report@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-3.0.1.tgz"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^5.0.4:
  version "5.0.6"
  resolved "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-5.0.6.tgz"
  integrity sha1-rK75SN93R8jrX78SZcuYD2NTpEE=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"

istanbul-reports@^3.1.6:
  version "3.1.7"
  resolved "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-3.1.7.tgz"
  integrity sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/iterator.prototype/download/iterator.prototype-1.1.5.tgz"
  integrity sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jake@^10.6.1, jake@^10.8.5:
  version "10.9.2"
  resolved "http://r.npm.sankuai.com/jake/download/jake-10.9.2.tgz"
  integrity sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-26.6.2.tgz"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-3.0.5.tgz"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^9.0.1:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-9.0.1.tgz"
  integrity sha1-LsQ5ZGWENSlvZ2GzThBnHC2VJ/Q=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^24.0.1:
  version "24.1.3"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-24.1.3.tgz"
  integrity sha1-iOSgfLndIQZ1FKYZ6fF7CQo5Sp8=
  dependencies:
    cssstyle "^4.0.1"
    data-urls "^5.0.0"
    decimal.js "^10.4.3"
    form-data "^4.0.0"
    html-encoding-sniffer "^4.0.0"
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.5"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.12"
    parse5 "^7.1.2"
    rrweb-cssom "^0.7.1"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^4.1.4"
    w3c-xmlserializer "^5.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^3.1.1"
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"
    ws "^8.18.0"
    xml-name-validator "^5.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

jsesc@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.0.2.tgz"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2module@^0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/json2module/download/json2module-0.0.3.tgz"
  integrity sha1-APtfSpt638PwZHwpyxe80Zeb6bI=
  dependencies:
    rw "^1.3.2"

json2mq@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jszip@^3.2.2:
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/jszip/download/jszip-3.10.1.tgz"
  integrity sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/lazy-cache/download/lazy-cache-1.0.4.tgz"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@~3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/lie/download/lie-3.3.0.tgz"
  integrity sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=
  dependencies:
    immediate "~3.0.5"

lilconfig@2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-2.1.0.tgz"
  integrity sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

lint-staged@^13.2.2:
  version "13.3.0"
  resolved "http://r.npm.sankuai.com/lint-staged/download/lint-staged-13.3.0.tgz"
  integrity sha1-eWXXKo1qbJMvhenBPM81lngtKKU=
  dependencies:
    chalk "5.3.0"
    commander "11.0.0"
    debug "4.3.4"
    execa "7.2.0"
    lilconfig "2.1.0"
    listr2 "6.6.1"
    micromatch "4.0.5"
    pidtree "0.6.0"
    string-argv "0.3.2"
    yaml "2.3.1"

listr2@6.6.1:
  version "6.6.1"
  resolved "http://r.npm.sankuai.com/listr2/download/listr2-6.6.1.tgz"
  integrity sha1-CLIynn6LpimEgUZJNwmfSizX+V0=
  dependencies:
    cli-truncate "^3.1.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^5.0.1"
    rfdc "^1.3.0"
    wrap-ansi "^8.1.0"

local-pkg@^0.5.0:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/local-pkg/download/local-pkg-0.5.1.tgz"
  integrity sha1-aWWGONKpUodTTUwv/3V5gBANu20=
  dependencies:
    mlly "^1.7.3"
    pkg-types "^1.2.1"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash-es/download/lodash-es-4.17.21.tgz"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.foreach/download/lodash.foreach-4.5.0.tgz"
  integrity sha1-Gmo16s5AEoDH8G3d7DUWWrJ+PlM=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.omit@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.omit/download/lodash.omit-4.5.0.tgz"
  integrity sha1-brGa5aHuHdnfC5aeZs4Lf6MLXmA=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash@^4.0.1, lodash@^4.17.11, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.4:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-5.0.1.tgz"
  integrity sha1-npKL9wyxg8HwyekdnmtxFdWXzgk=
  dependencies:
    ansi-escapes "^5.0.0"
    cli-cursor "^4.0.0"
    slice-ansi "^5.0.0"
    strip-ansi "^7.0.1"
    wrap-ansi "^8.0.1"

longest-streak@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/longest-streak/download/longest-streak-3.1.0.tgz"
  integrity sha1-YvpnzZWHQqFXSvnzmGY2QQLZDNQ=

longest@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/longest/download/longest-1.0.1.tgz"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loupe@^2.3.6, loupe@^2.3.7:
  version "2.3.7"
  resolved "http://r.npm.sankuai.com/loupe/download/loupe-2.3.7.tgz"
  integrity sha1-bmm31Nt9OrQ2MoAT030cjDVAxpc=
  dependencies:
    get-func-name "^2.0.1"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/lower-case/download/lower-case-2.0.2.tgz"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lz-string@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/lz-string/download/lz-string-1.5.0.tgz"
  integrity sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=

magic-bytes.js@^1.0.14:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/magic-bytes.js/download/magic-bytes.js-1.10.0.tgz"
  integrity sha1-xBz0vC+AKZKwXmSWJBHJ3UT975I=

magic-string@^0.27.0:
  version "0.27.0"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.27.0.tgz#e4a3413b4bab6d98d2becffd48b4a257effdbbf3"
  integrity sha1-5KNBO0urbZjSvs/9SLSiV+/9u/M=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

magic-string@^0.30.5:
  version "0.30.17"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

magicast@^0.3.3:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/magicast/download/magicast-0.3.5.tgz"
  integrity sha1-gwHDx9ZnBKB3HrG610J08OwDZzk=
  dependencies:
    "@babel/parser" "^7.25.4"
    "@babel/types" "^7.25.4"
    source-map-js "^1.2.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-4.0.0.tgz"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

markdown-table@^3.0.0:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/markdown-table/download/markdown-table-3.0.4.tgz"
  integrity sha1-/kTW1BD/nW8uoXl6P2CqTStjHCo=

marked@^9.1.6:
  version "9.1.6"
  resolved "http://r.npm.sankuai.com/marked/download/marked-9.1.6.tgz"
  integrity sha1-XSo/gYCr+8XWLjJYo4ocGcA4FpU=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

mdast-util-find-and-replace@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/mdast-util-find-and-replace/download/mdast-util-find-and-replace-3.0.2.tgz"
  integrity sha1-cKMXTIlOFN9yKr9DvCUMuuRLEd8=
  dependencies:
    "@types/mdast" "^4.0.0"
    escape-string-regexp "^5.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

mdast-util-from-markdown@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/mdast-util-from-markdown/download/mdast-util-from-markdown-2.0.2.tgz"
  integrity sha1-SFA5DKfPF0E6m5oPvvzRvA60Fgo=
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-gfm-autolink-literal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-autolink-literal/download/mdast-util-gfm-autolink-literal-2.0.1.tgz"
  integrity sha1-q9VXYwM3vTCm1aS9glLhwtwIddU=
  dependencies:
    "@types/mdast" "^4.0.0"
    ccount "^2.0.0"
    devlop "^1.0.0"
    mdast-util-find-and-replace "^3.0.0"
    micromark-util-character "^2.0.0"

mdast-util-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-footnote/download/mdast-util-gfm-footnote-2.1.0.tgz"
  integrity sha1-d3jp2co99yOMwr0/orG/amWxlAM=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"

mdast-util-gfm-strikethrough@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-strikethrough/download/mdast-util-gfm-strikethrough-2.0.0.tgz"
  integrity sha1-1E756O0oOsjBFlqw0N/QWMJ2TBY=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-table@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-table/download/mdast-util-gfm-table-2.0.0.tgz"
  integrity sha1-ekNftiI6crCGKzOvvXErba6HjTg=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    markdown-table "^3.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-task-list-item@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-task-list-item/download/mdast-util-gfm-task-list-item-2.0.0.tgz"
  integrity sha1-5oCV0vikMD7yQJSrZC4QR7mRqTY=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm/download/mdast-util-gfm-3.1.0.tgz"
  integrity sha1-LN9juSwqMxQGsPsNtMB3wbAzF1E=
  dependencies:
    mdast-util-from-markdown "^2.0.0"
    mdast-util-gfm-autolink-literal "^2.0.0"
    mdast-util-gfm-footnote "^2.0.0"
    mdast-util-gfm-strikethrough "^2.0.0"
    mdast-util-gfm-table "^2.0.0"
    mdast-util-gfm-task-list-item "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-expression@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-mdx-expression/download/mdast-util-mdx-expression-2.0.1.tgz"
  integrity sha1-Q/CrrJrcdW4ghvY4IqOMjTw6UJY=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-jsx@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/mdast-util-mdx-jsx/download/mdast-util-mdx-jsx-3.2.0.tgz"
  integrity sha1-/QTGeip0me+5BailxXjd3J/a2g0=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    ccount "^2.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    parse-entities "^4.0.0"
    stringify-entities "^4.0.0"
    unist-util-stringify-position "^4.0.0"
    vfile-message "^4.0.0"

mdast-util-mdxjs-esm@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-mdxjs-esm/download/mdast-util-mdxjs-esm-2.0.1.tgz"
  integrity sha1-AZz751etYt1VfbNaaV5zFLzJ+pc=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-phrasing/download/mdast-util-phrasing-4.1.0.tgz"
  integrity sha1-fMCo3sMOrwS3salmGpKtszgqpuM=
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-hast@^13.0.0:
  version "13.2.0"
  resolved "http://r.npm.sankuai.com/mdast-util-to-hast/download/mdast-util-to-hast-13.2.0.tgz"
  integrity sha1-XKWOW5IcwKPe0bwC7teaT+T+QfQ=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    devlop "^1.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    trim-lines "^3.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/mdast-util-to-markdown/download/mdast-util-to-markdown-2.1.2.tgz"
  integrity sha1-+RD/5giX8Eu0t+fuQ0SG92KINhs=
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-to-string/download/mdast-util-to-string-4.0.0.tgz"
  integrity sha1-elEhR1VWoE5+3etnsmSq550xKBQ=
  dependencies:
    "@types/mdast" "^4.0.0"

"memoize-one@>=3.1.1 <6", memoize-one@^5.1.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/memoize-one/download/memoize-one-5.2.1.tgz"
  integrity sha1-gzeqPEM1WBg57AHD1ZQJDOvo8A4=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromark-core-commonmark@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/micromark-core-commonmark/download/micromark-core-commonmark-2.0.3.tgz"
  integrity sha1-xpFjDkhQIaaM8o28Kyyifr9njNQ=
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-autolink-literal@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-autolink-literal/download/micromark-extension-gfm-autolink-literal-2.1.0.tgz"
  integrity sha1-Yoau6WhsRGLB41UqnVBf7dzuuTU=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-footnote/download/micromark-extension-gfm-footnote-2.1.0.tgz"
  integrity sha1-TatW1OOYuYU/b+TvrE/JNh8+B1A=
  dependencies:
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-strikethrough@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-strikethrough/download/micromark-extension-gfm-strikethrough-2.1.0.tgz"
  integrity sha1-hhBt+LOmkrX2qSKA04eb5r5G2SM=
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-table@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-table/download/micromark-extension-gfm-table-2.1.1.tgz"
  integrity sha1-+scLy/Uf5l9fRAMxGNOb6Km1lAs=
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-tagfilter@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-tagfilter/download/micromark-extension-gfm-tagfilter-2.0.0.tgz"
  integrity sha1-8m2KeAe1mF+6E89hRltYyl/33Fc=
  dependencies:
    micromark-util-types "^2.0.0"

micromark-extension-gfm-task-list-item@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-task-list-item/download/micromark-extension-gfm-task-list-item-2.1.0.tgz"
  integrity sha1-vMNNgFY5gpmQ7BdcPuoSu1t4Hyw=
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm/download/micromark-extension-gfm-3.0.0.tgz"
  integrity sha1-PhM3arld16XP0OKVYN/pmWV7PFs=
  dependencies:
    micromark-extension-gfm-autolink-literal "^2.0.0"
    micromark-extension-gfm-footnote "^2.0.0"
    micromark-extension-gfm-strikethrough "^2.0.0"
    micromark-extension-gfm-table "^2.0.0"
    micromark-extension-gfm-tagfilter "^2.0.0"
    micromark-extension-gfm-task-list-item "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-destination/download/micromark-factory-destination-2.0.1.tgz"
  integrity sha1-j++OD3CB8EdPvdkt61DJkKAmRjk=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-label/download/micromark-factory-label-2.0.1.tgz"
  integrity sha1-UmfvqX8eUlTvx/ILRZo4yyEFi6E=
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-space/download/micromark-factory-space-2.0.1.tgz"
  integrity sha1-NtAhLpYrKzEh+FJfx6PHwCnzNPw=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-title/download/micromark-factory-title-2.0.1.tgz"
  integrity sha1-I35KpdWKlYY/AQMtnumwkPHebpQ=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-whitespace/download/micromark-factory-whitespace-2.0.1.tgz"
  integrity sha1-BrJrKYPE0nv8xlezPiUTTUhosLE=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/micromark-util-character/download/micromark-util-character-2.1.1.tgz"
  integrity sha1-L5h4MaQNTFEKwmHomFLE6XA8zaY=
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-chunked/download/micromark-util-chunked-2.0.1.tgz"
  integrity sha1-R/vNk0caP8yrhs/wOEf8NVLbEFE=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-classify-character/download/micromark-util-classify-character-2.0.1.tgz"
  integrity sha1-05n6+cRcoUyLS+mLHqSBvO2Htik=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-combine-extensions/download/micromark-util-combine-extensions-2.0.1.tgz"
  integrity sha1-Kg9JCrCL/1zC/V7sbdDKBPibMKk=
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/micromark-util-decode-numeric-character-reference/download/micromark-util-decode-numeric-character-reference-2.0.2.tgz"
  integrity sha1-/PFbZgl5OI5vEYzba/fXnXPSb+U=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-decode-string/download/micromark-util-decode-string-2.0.1.tgz"
  integrity sha1-bLmVguXScehO/KjmGoB5lNcWHrI=
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-encode/download/micromark-util-encode-2.0.1.tgz"
  integrity sha1-DVHRwJVVHPqsNoMmljz1XxX1QLg=

micromark-util-html-tag-name@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-html-tag-name/download/micromark-util-html-tag-name-2.0.1.tgz"
  integrity sha1-5AQDCWSBmGtBwQZif5j3LU0QuCU=

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-normalize-identifier/download/micromark-util-normalize-identifier-2.0.1.tgz"
  integrity sha1-ww13sugyrPZSb4vxqke8nJQ4wW0=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-resolve-all/download/micromark-util-resolve-all-2.0.1.tgz"
  integrity sha1-4aLWLN0jcjCirhGDkCexk4HjHos=
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-sanitize-uri/download/micromark-util-sanitize-uri-2.0.1.tgz"
  integrity sha1-q4l4m4GKWHUrc9a1UjhiG3+qj9c=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-util-subtokenize/download/micromark-util-subtokenize-2.1.0.tgz"
  integrity sha1-2K3lug8xl6HPaimZ+7/mNXoaGe4=
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-symbol/download/micromark-util-symbol-2.0.1.tgz"
  integrity sha1-5dpJTo6ysHGg0I+zT2zv7GwKGbg=

micromark-util-types@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/micromark-util-types/download/micromark-util-types-2.0.2.tgz"
  integrity sha1-8AIl9fWg68MlT5bDa2YFxLOTkI4=

micromark@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/micromark/download/micromark-4.0.2.tgz"
  integrity sha1-kTlaPhiEoZjmIRbjPJxWjjmTb9s=
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@4.0.5:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-match@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/mime-match/download/mime-match-1.0.2.tgz"
  integrity sha1-P4fDHprxpf1IX7nbE0Qosju7e6g=
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12, mime-types@~2.1.19:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-4.0.0.tgz"
  integrity sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.5, minimist@~1.2.8:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mlly@^1.7.3:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/mlly/download/mlly-1.7.4.tgz"
  integrity sha1-PXKV6iNY7HonHqpdAAoPhP6+EA8=
  dependencies:
    acorn "^8.14.0"
    pathe "^2.0.1"
    pkg-types "^1.3.0"
    ufo "^1.5.4"

mock-property@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/mock-property/download/mock-property-1.0.3.tgz"
  integrity sha1-PjfFClZgnVSMq9VlWf3j3YdnsQw=
  dependencies:
    define-data-property "^1.1.1"
    functions-have-names "^1.2.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    hasown "^2.0.0"
    isarray "^2.0.5"

moment@^2.24.0, moment@^2.29.4:
  version "2.30.1"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

monaco-editor@^0.52.2:
  version "0.52.2"
  resolved "http://r.npm.sankuai.com/monaco-editor/download/monaco-editor-0.52.2.tgz"
  integrity sha1-U8dab8xoAmhOmf0bJwApmFcAIgU=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/namespace-emitter/download/namespace-emitter-2.0.1.tgz"
  integrity sha1-l41RNhxhMTtOa4z284U9CN+isXw=

nanoid@^3.1.25, nanoid@^3.2.0, nanoid@^3.3.8:
  version "3.3.8"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.8.tgz"
  integrity sha1-sb4wML7jaq/xi6yzdeXM5SFoS68=

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/next-tick/download/next-tick-1.1.0.tgz"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

no-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/no-case/download/no-case-3.0.4.tgz"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz"
  integrity sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-5.3.0.tgz"
  integrity sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=
  dependencies:
    path-key "^4.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.12:
  version "2.2.16"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.16.tgz"
  integrity sha1-F3dgu6AsNR3x0mROIgwx3+yM20M=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3:
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.3.tgz"
  integrity sha1-8UwYPeURMCQ9bRiuFJN1/1DqSIo=

object-inspect@~1.12.3:
  version "1.12.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.3.tgz"
  integrity sha1-umLf/WfuJWyMCG365p4BbNHxmLk=

object-is@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/object-is/download/object-is-1.1.6.tgz"
  integrity sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-path@^0.11.4:
  version "0.11.8"
  resolved "http://r.npm.sankuai.com/object-path/download/object-path-0.11.8.tgz"
  integrity sha1-7QAsArvdAHC3iidFXorgH8FNR0I=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.8.tgz"
  integrity sha1-v/5vKC4B9NF4ByBKJPjt2CNZnEE=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-6.0.0.tgz"
  integrity sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=
  dependencies:
    mimic-fn "^4.0.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.4.0, ora@^5.4.1:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

own-keys@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

owner@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/owner/download/owner-0.1.0.tgz"
  integrity sha1-FNkRRrRFoRDdROwjtbpK9sPNvWQ=

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-limit@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-5.0.0.tgz"
  integrity sha1-aUbVtxQLZJt6M6An2JtMYls6WYU=
  dependencies:
    yocto-queue "^1.0.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

pako@~1.0.2:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parchment@^1.1.2, parchment@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-1.1.4.tgz"
  integrity sha1-rt7Xq5OP6SHUw0vDOc4RaLwv/eU=

parchment@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-3.0.0.tgz"
  integrity sha1-LjpK2kVOEgaudup6/LUOn7UX59Y=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-entities@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/parse-entities/download/parse-entities-4.0.2.tgz"
  integrity sha1-YdRvXtKOTuYundxD1rAQGIRD8Vk=
  dependencies:
    "@types/unist" "^2.0.0"
    character-entities-legacy "^3.0.0"
    character-reference-invalid "^2.0.0"
    decode-named-character-reference "^1.0.0"
    is-alphanumerical "^2.0.0"
    is-decimal "^2.0.0"
    is-hexadecimal "^2.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-7.1.0.tgz"
  integrity sha1-tagGVI7Yk6Q+JMy0L7t4BpMR6Bs=
  dependencies:
    domhandler "^5.0.3"
    parse5 "^7.0.0"

parse5@^7.0.0, parse5@^7.1.2:
  version "7.2.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-7.2.1.tgz"
  integrity sha1-iSj1WRXmEl9DDMRDCXZb8XVWozo=
  dependencies:
    entities "^4.5.0"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-key@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-4.0.0.tgz"
  integrity sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@^1.7.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pathe@^1.1.1, pathe@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-1.1.2.tgz"
  integrity sha1-bEy0epRWkuSKHd1uQJTRcFFkN+w=

pathe@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-2.0.1.tgz"
  integrity sha1-7h5pZcXM/JjcWks2amum3WJKM9Y=

pathval@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/pathval/download/pathval-1.1.1.tgz"
  integrity sha1-hTTnenfOesWiUS6iHg/bj89sPY0=

pdfast@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/pdfast/download/pdfast-0.2.0.tgz"
  integrity sha1-jLxVbhvyUiF3eHwN4uDUNzuohck=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.2.tgz"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pidtree@0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/pidtree/download/pidtree-0.6.0.tgz"
  integrity sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pkg-types@^1.2.1, pkg-types@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/pkg-types/download/pkg-types-1.3.0.tgz"
  integrity sha1-U9kV65lIV5jFVK2Ostwq98AwBus=
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.3"
    pathe "^1.1.2"

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz"
  integrity sha1-ibtjxvraLD6QrcSmR77us5zHv48=

postcss@^8.4.43:
  version "8.5.0"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.0.tgz"
  integrity sha1-FSRLn9ZfgJsoGWgkVvDn4eMMFFs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

preact@^10.5.13:
  version "10.25.4"
  resolved "http://r.npm.sankuai.com/preact/download/preact-10.25.4.tgz"
  integrity sha1-wdAL7p17nc0GojEdmVGXO1Bq6Kw=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.7.1:
  version "2.8.8"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

pretty-format@^27.0.2:
  version "27.5.1"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-27.5.1.tgz"
  integrity sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^29.7.0:
  version "29.7.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-29.7.0.tgz"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

prismjs@^1.23.0:
  version "1.29.0"
  resolved "http://r.npm.sankuai.com/prismjs/download/prismjs-1.29.0.tgz"
  integrity sha1-8RNVWo+ptXw15je7onUJ3PgC3RI=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

prop-types@^15.5.10, prop-types@^15.5.8, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-information@^6.0.0:
  version "6.5.0"
  resolved "http://r.npm.sankuai.com/property-information/download/property-information-6.5.0.tgz"
  integrity sha1-YhL7tSunV+ku9PudZXVjuTO3/+w=

property-information@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/property-information/download/property-information-7.0.0.tgz"
  integrity sha1-NQim1rC46zym6yxmI7Fk0u0qsRI=

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

psl@^1.1.28, psl@^1.1.33:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz"
  integrity sha1-vazjGJbx2XzsannoIkiYzpPZdMY=
  dependencies:
    punycode "^2.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

qs@^6.11.0:
  version "6.13.1"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.13.1.tgz"
  integrity sha1-POX8cr06gXG4XJm5PGXdILfRsW4=
  dependencies:
    side-channel "^1.0.6"

qs@~6.5.2:
  version "6.5.3"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-3.6.3.tgz"
  integrity sha1-sZ/SuJQSMBxg4f8hPY2GDqwPEDI=
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill-delta@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-5.1.0.tgz"
  integrity sha1-HEvAj3yOXMS9yIoVoacMHMctK0g=
  dependencies:
    fast-diff "^1.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.isequal "^4.5.0"

quill-image-resize-module-react@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/quill-image-resize-module-react/download/quill-image-resize-module-react-3.0.0.tgz"
  integrity sha1-3sMs7NF1/NxSv9GEIGF0/dERqGQ=
  dependencies:
    lodash "^4.17.4"
    quill "^1.2.2"
    raw-loader "^0.5.1"

quill@^1.2.2, quill@^1.3.7:
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/quill/download/quill-1.3.7.tgz"
  integrity sha1-2lsvOixHDpMjQM2/NmjJ8h+Shug=
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

quill@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/quill/download/quill-2.0.3.tgz"
  integrity sha1-dSdlox1aU1zcVxfcSdTlAJk2XrE=
  dependencies:
    eventemitter3 "^5.0.1"
    lodash-es "^4.17.21"
    parchment "^3.0.0"
    quill-delta "^5.1.0"

raf-schd@^4.0.2:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/raf-schd/download/raf-schd-4.0.3.tgz"
  integrity sha1-XWw070b4sqDogKj823Q+/Fv9vBo=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

raw-loader@^0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/raw-loader/download/raw-loader-0.5.1.tgz"
  integrity sha1-DD0L6u2KAclm2Xh793goElKpeao=

rc-cascader@~3.33.0:
  version "3.33.0"
  resolved "http://r.npm.sankuai.com/rc-cascader/download/rc-cascader-3.33.0.tgz"
  integrity sha1-rN6v67339ylvTYSYDQLPCDX5ORA=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "^2.3.1"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-checkbox@~3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/rc-checkbox/download/rc-checkbox-3.5.0.tgz"
  integrity sha1-OuJEHjoyF3TTkPdlOecGhk/PX/A=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.25.2"

rc-collapse@~3.9.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/rc-collapse/download/rc-collapse-3.9.0.tgz"
  integrity sha1-lyQEznck4cnR0kdlQ+EXVASjaAY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.27.0"

rc-dialog@~9.6.0:
  version "9.6.0"
  resolved "http://r.npm.sankuai.com/rc-dialog/download/rc-dialog-9.6.0.tgz"
  integrity sha1-3HolXGrRy1YCHDphx96G7ojHw3E=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.21.0"

rc-drawer@~7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/rc-drawer/download/rc-drawer-7.2.0.tgz"
  integrity sha1-jX3i8f1S86xaJfVK+7isFMYuVmM=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    classnames "^2.2.6"
    rc-motion "^2.6.1"
    rc-util "^5.38.1"

rc-dropdown@~4.2.0, rc-dropdown@~4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/rc-dropdown/download/rc-dropdown-4.2.1.tgz"
  integrity sha1-RHKesqQnLgNT0xrAYNoh5gasyxw=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-util "^5.44.1"

rc-field-form@~1.38.2:
  version "1.38.2"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-1.38.2.tgz"
  integrity sha1-Hq+smOuE1H3DtV3pjtUHUdmFLdI=
  dependencies:
    "@babel/runtime" "^7.18.0"
    async-validator "^4.1.0"
    rc-util "^5.32.2"

rc-field-form@~2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-2.7.0.tgz"
  integrity sha1-IkE+eT81v8HzWw7EYndNcnf1o5k=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    rc-util "^5.32.2"

rc-image@~7.11.0:
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/rc-image/download/rc-image-7.11.0.tgz"
  integrity sha1-GMd+pVem/b4mhWxoipqs4VBcDnc=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    classnames "^2.2.6"
    rc-dialog "~9.6.0"
    rc-motion "^2.6.2"
    rc-util "^5.34.1"

rc-input-number@~9.4.0:
  version "9.4.0"
  resolved "http://r.npm.sankuai.com/rc-input-number/download/rc-input-number-9.4.0.tgz"
  integrity sha1-ZcrwTxttBfR+FBsfX0hHJMH3/Vo=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    classnames "^2.2.5"
    rc-input "~1.7.1"
    rc-util "^5.40.1"

rc-input@~1.7.1, rc-input@~1.7.2:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/rc-input/download/rc-input-1.7.2.tgz"
  integrity sha1-pB1coUAhR105mN6wljDuRiaGEK8=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-mentions@~2.19.1:
  version "2.19.1"
  resolved "http://r.npm.sankuai.com/rc-mentions/download/rc-mentions-2.19.1.tgz"
  integrity sha1-P9DdC/PdY6/baiF1DLroHzgkucQ=
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-input "~1.7.1"
    rc-menu "~9.16.0"
    rc-textarea "~1.9.0"
    rc-util "^5.34.1"

rc-menu@~9.16.0:
  version "9.16.0"
  resolved "http://r.npm.sankuai.com/rc-menu/download/rc-menu-9.16.0.tgz"
  integrity sha1-U2R/YPUTv6Cb/BrMvZao3ySQASE=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.3.1"
    rc-util "^5.27.0"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4, rc-motion@^2.6.1, rc-motion@^2.6.2, rc-motion@^2.9.0, rc-motion@^2.9.5:
  version "2.9.5"
  resolved "http://r.npm.sankuai.com/rc-motion/download/rc-motion-2.9.5.tgz"
  integrity sha1-Esbq1P01X5TwDem7TxXfV21nfgw=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.44.0"

rc-notification@~5.6.2:
  version "5.6.2"
  resolved "http://r.npm.sankuai.com/rc-notification/download/rc-notification-5.6.2.tgz"
  integrity sha1-hSWzLUndluyXSsrmHR0eq95hRjo=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.9.0"
    rc-util "^5.20.1"

rc-overflow@^1.3.1, rc-overflow@^1.3.2:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rc-overflow/download/rc-overflow-1.4.1.tgz"
  integrity sha1-4bzwN1l5wkz/oth7+DoZ3tX830U=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.37.0"

rc-pagination@~5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/rc-pagination/download/rc-pagination-5.0.0.tgz"
  integrity sha1-djPh8P83KteMA+hrzveLZgN00ZY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.38.0"

rc-picker@~4.9.2:
  version "4.9.2"
  resolved "http://r.npm.sankuai.com/rc-picker/download/rc-picker-4.9.2.tgz"
  integrity sha1-TdjiP8qxB7RPBgTWhMa6Ehaeo14=
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.1"
    rc-overflow "^1.3.2"
    rc-resize-observer "^1.4.0"
    rc-util "^5.43.0"

rc-progress@^3.5.1:
  version "3.5.1"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.5.1.tgz"
  integrity sha1-o839L+BOtcPUP6HGnn3XDHOxAq4=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~3.2.1:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.2.4.tgz"
  integrity sha1-QDas2uJWZDhUW8TfIgMki6uvdUk=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-4.0.0.tgz"
  integrity sha1-U4IUfZrdM9Ol+9JkABNz32RA4SY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-rate@~2.13.0:
  version "2.13.0"
  resolved "http://r.npm.sankuai.com/rc-rate/download/rc-rate-2.13.0.tgz"
  integrity sha1-ZC9ZHM9Vw6XYTY0hLK8feVHSA6g=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.0, rc-resize-observer@^1.3.1, rc-resize-observer@^1.4.0, rc-resize-observer@^1.4.3:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/rc-resize-observer/download/rc-resize-observer-1.4.3.tgz"
  integrity sha1-T9QfpWG6UTYrUVWgfDXXyJoepWk=
  dependencies:
    "@babel/runtime" "^7.20.7"
    classnames "^2.2.1"
    rc-util "^5.44.1"
    resize-observer-polyfill "^1.5.1"

rc-segmented@~2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/rc-segmented/download/rc-segmented-2.7.0.tgz"
  integrity sha1-9WwgRKv48DlYs6mp0ymH8Q3MT8Q=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-motion "^2.4.4"
    rc-util "^5.17.0"

rc-select@~14.16.2, rc-select@~14.16.5:
  version "14.16.5"
  resolved "http://r.npm.sankuai.com/rc-select/download/rc-select-14.16.5.tgz"
  integrity sha1-ioRx4jXa1pDVXTXcWzG//9RsHDI=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.3.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.2"

rc-slider@~11.1.8:
  version "11.1.8"
  resolved "http://r.npm.sankuai.com/rc-slider/download/rc-slider-11.1.8.tgz"
  integrity sha1-zzsw2srI+Y1E92hfcz9vfaFG/AY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.36.0"

rc-steps@~6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/rc-steps/download/rc-steps-6.0.1.tgz"
  integrity sha1-whNs0Ah3M/bVCSCahKXIDcKaJ00=
  dependencies:
    "@babel/runtime" "^7.16.7"
    classnames "^2.2.3"
    rc-util "^5.16.1"

rc-switch@~4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/rc-switch/download/rc-switch-4.1.0.tgz"
  integrity sha1-832BtODFr9EnT9hTZ7FzBr8l59c=
  dependencies:
    "@babel/runtime" "^7.21.0"
    classnames "^2.2.1"
    rc-util "^5.30.0"

rc-table@~7.27.2:
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/rc-table/download/rc-table-7.27.2.tgz"
  integrity sha1-/aji1/dCRW3tQKVDmKKGJOFof20=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.22.5"
    shallowequal "^1.1.0"

rc-table@~7.50.2:
  version "7.50.2"
  resolved "http://r.npm.sankuai.com/rc-table/download/rc-table-7.50.2.tgz"
  integrity sha1-1munHdXDT/iYElWvwLmOSNOKipc=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.44.3"
    rc-virtual-list "^3.14.2"

rc-tabs@~15.5.0:
  version "15.5.0"
  resolved "http://r.npm.sankuai.com/rc-tabs/download/rc-tabs-15.5.0.tgz"
  integrity sha1-d4QF9o6MM3dSNeZmvXzQlEn37us=
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "~4.2.0"
    rc-menu "~9.16.0"
    rc-motion "^2.6.2"
    rc-resize-observer "^1.0.0"
    rc-util "^5.34.1"

rc-textarea@~1.9.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/rc-textarea/download/rc-textarea-1.9.0.tgz"
  integrity sha1-2AcZTr75DyXwuVAc3fXo8paNWYo=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-input "~1.7.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.27.0"

rc-tooltip@~6.3.2:
  version "6.3.2"
  resolved "http://r.npm.sankuai.com/rc-tooltip/download/rc-tooltip-6.3.2.tgz"
  integrity sha1-T8Cvd3MbflcfXOFb0e3nWbDmTdc=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.1"

rc-tree-select@~5.27.0:
  version "5.27.0"
  resolved "http://r.npm.sankuai.com/rc-tree-select/download/rc-tree-select-5.27.0.tgz"
  integrity sha1-PapilyroCEbayWv0d20ancnHxMY=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "2.x"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-tree@~5.13.0:
  version "5.13.0"
  resolved "http://r.npm.sankuai.com/rc-tree/download/rc-tree-5.13.0.tgz"
  integrity sha1-rjR2jBRj/R+xnXNUnCmyGciJEpY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.1"

rc-upload@~4.8.1:
  version "4.8.1"
  resolved "http://r.npm.sankuai.com/rc-upload/download/rc-upload-4.8.1.tgz"
  integrity sha1-rFXyvBAblbUqbkfzwY8PVbVOFtI=
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.16.1, rc-util@^5.17.0, rc-util@^5.18.1, rc-util@^5.2.0, rc-util@^5.20.1, rc-util@^5.21.0, rc-util@^5.22.5, rc-util@^5.24.4, rc-util@^5.25.2, rc-util@^5.27.0, rc-util@^5.30.0, rc-util@^5.31.1, rc-util@^5.32.2, rc-util@^5.34.1, rc-util@^5.35.0, rc-util@^5.36.0, rc-util@^5.37.0, rc-util@^5.38.0, rc-util@^5.38.1, rc-util@^5.40.1, rc-util@^5.43.0, rc-util@^5.44.0, rc-util@^5.44.1, rc-util@^5.44.3:
  version "5.44.3"
  resolved "http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.3.tgz"
  integrity sha1-nspQOZBkRhE8QDKFn4jBUjRUeWE=
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-virtual-list@^3.14.2, rc-virtual-list@^3.16.0, rc-virtual-list@^3.5.1, rc-virtual-list@^3.5.2:
  version "3.17.0"
  resolved "http://r.npm.sankuai.com/rc-virtual-list/download/rc-virtual-list-3.17.0.tgz"
  integrity sha1-Zp4pJ3BAwgtpE7u3b0nqCpLBE28=
  dependencies:
    "@babel/runtime" "^7.20.0"
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.36.0"

react-beautiful-dnd@13.0.0:
  version "13.0.0"
  resolved "http://r.npm.sankuai.com/react-beautiful-dnd/download/react-beautiful-dnd-13.0.0.tgz"
  integrity sha1-9wzI/4K4S8cY+K8VfJ+VdXpsO0A=
  dependencies:
    "@babel/runtime" "^7.8.4"
    css-box-model "^1.2.0"
    memoize-one "^5.1.1"
    raf-schd "^4.0.2"
    react-redux "^7.1.1"
    redux "^4.0.4"
    use-memo-one "^1.1.1"

react-beautiful-dnd@^13.1.1:
  version "13.1.1"
  resolved "http://r.npm.sankuai.com/react-beautiful-dnd/download/react-beautiful-dnd-13.1.1.tgz"
  integrity sha1-sPMIelhAkgq/i7IyXx/6RtjE0KI=
  dependencies:
    "@babel/runtime" "^7.9.2"
    css-box-model "^1.2.0"
    memoize-one "^5.1.1"
    raf-schd "^4.0.2"
    react-redux "^7.2.0"
    redux "^4.0.4"
    use-memo-one "^1.1.1"

react-click-outside@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/react-click-outside/download/react-click-outside-3.0.1.tgz"
  integrity sha1-bnfoTS8Xr6qsJtutdDy7+Qn14kw=
  dependencies:
    hoist-non-react-statics "^2.1.1"

react-content-loader@^5.0.4:
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/react-content-loader/download/react-content-loader-5.1.4.tgz"
  integrity sha1-hUuv5EFd2d4HF0YhN1vDCO3Q67U=

react-dnd-html5-backend@^16.0.1:
  version "16.0.1"
  resolved "http://r.npm.sankuai.com/react-dnd-html5-backend/download/react-dnd-html5-backend-16.0.1.tgz"
  integrity sha1-h/rvFYRdUSojs8CNKez9NIcWiLY=
  dependencies:
    dnd-core "^16.0.1"

react-dnd@^16.0.1:
  version "16.0.1"
  resolved "http://r.npm.sankuai.com/react-dnd/download/react-dnd-16.0.1.tgz"
  integrity sha1-JEKj7GeJLGDUChVZ7vRUmLom+jc=
  dependencies:
    "@react-dnd/invariant" "^4.0.1"
    "@react-dnd/shallowequal" "^4.0.1"
    dnd-core "^16.0.1"
    fast-deep-equal "^3.1.3"
    hoist-non-react-statics "^3.3.2"

react-dom@^18.3.1:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz#c2265d79511b57d479b3dd3fdfa51536494c5cb4"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-drag-listview@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-drag-listview/download/react-drag-listview-2.0.0.tgz"
  integrity sha1-uOfsX5gOy786u4X1DbCwPNdk7b8=
  dependencies:
    babel-runtime "^6.26.0"
    prop-types "^15.5.8"

react-fast-compare@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-2.0.4.tgz"
  integrity sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk=

react-fast-compare@^3.0.1, react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-3.2.2.tgz"
  integrity sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=

react-fast-marquee@^1.2.1:
  version "1.6.5"
  resolved "http://r.npm.sankuai.com/react-fast-marquee/download/react-fast-marquee-1.6.5.tgz"
  integrity sha1-mJKa6T7vCHpgenHp1Fq3a7qX3BY=

react-infinite-scroll-component@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/react-infinite-scroll-component/download/react-infinite-scroll-component-6.1.0.tgz"
  integrity sha1-flEeeqD3KKw+UfZKOKYHmsUiQH8=
  dependencies:
    throttle-debounce "^2.1.0"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^17.0.1, react-is@^17.0.2:
  version "17.0.2"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-17.0.2.tgz"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^18.0.0, react-is@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-load-script@0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/react-load-script/download/react-load-script-0.0.6.tgz"
  integrity sha1-22hRI2qqJbtiJnei61Ha1PjSwlg=

react-markdown@^10.1.0:
  version "10.1.0"
  resolved "http://r.npm.sankuai.com/react-markdown/download/react-markdown-10.1.0.tgz"
  integrity sha1-4ivCD63bwHYFwVKEJVZTwPO61co=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    hast-util-to-jsx-runtime "^2.0.0"
    html-url-attributes "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    remark-parse "^11.0.0"
    remark-rehype "^11.0.0"
    unified "^11.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

react-monaco-editor@^0.58.0:
  version "0.58.0"
  resolved "http://r.npm.sankuai.com/react-monaco-editor/download/react-monaco-editor-0.58.0.tgz"
  integrity sha1-2iKF0n0pvhCAt1WMJeD8UFDj/fs=

react-popper@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/react-popper/download/react-popper-2.3.0.tgz"
  integrity sha1-F4kcYg4TINzjGLrZ/t5GpfcccLo=
  dependencies:
    react-fast-compare "^3.0.1"
    warning "^4.0.2"

react-quill@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-quill/download/react-quill-2.0.0.tgz"
  integrity sha1-Z6AQD1j5aiRq8kDJ+mhBs2Oz4Bc=
  dependencies:
    "@types/quill" "^1.3.10"
    lodash "^4.17.4"
    quill "^1.3.7"

react-redux@^7.1.1, react-redux@^7.2.0:
  version "7.2.9"
  resolved "http://r.npm.sankuai.com/react-redux/download/react-redux-7.2.9.tgz"
  integrity sha1-CUiPu5QWpO/jc1tyNQVUQrBCSB0=
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    hoist-non-react-statics "^3.3.2"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^17.0.2"

react-resize-detector@^4.2.1:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/react-resize-detector/download/react-resize-detector-4.2.3.tgz"
  integrity sha1-ffJYZoowvf2I5lW72yfbf9eyMSc=
  dependencies:
    lodash "^4.17.15"
    lodash-es "^4.17.15"
    prop-types "^15.7.2"
    raf-schd "^4.0.2"
    resize-observer-polyfill "^1.5.1"

react-router-dom@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router-dom/download/react-router-dom-4.3.1.tgz"
  integrity sha1-TCYZ/CTE+ofJ/Rj0+0pD/mP71cY=
  dependencies:
    history "^4.7.2"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    prop-types "^15.6.1"
    react-router "^4.3.1"
    warning "^4.0.1"

react-router@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router/download/react-router-4.3.1.tgz"
  integrity sha1-qtpK7xTICcsuaGsFzuR0IjRQbE4=
  dependencies:
    history "^4.7.2"
    hoist-non-react-statics "^2.5.0"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.1"
    warning "^4.0.1"

react-slick@^0.30.2:
  version "0.30.3"
  resolved "http://r.npm.sankuai.com/react-slick/download/react-slick-0.30.3.tgz"
  integrity sha1-OvWEb8vATGgfi6kvSIgaD3gSSic=
  dependencies:
    classnames "^2.2.5"
    enquire.js "^2.1.6"
    json2mq "^0.2.0"
    lodash.debounce "^4.0.8"
    resize-observer-polyfill "^1.5.0"

react-transition-group@^2.5.3:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/react-transition-group/download/react-transition-group-2.9.0.tgz"
  integrity sha1-35zbAleWIRFRpDbGmo87l7WwfI0=
  dependencies:
    dom-helpers "^3.4.0"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"
    react-lifecycles-compat "^3.0.4"

react-window@^1.8.8:
  version "1.8.11"
  resolved "http://r.npm.sankuai.com/react-window/download/react-window-1.8.11.tgz"
  integrity sha1-qFe0j6hb13BC1ZzEYJZP8uBkhSU=
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

"react@^16.12.0 || ^17.0.0":
  version "17.0.2"
  resolved "http://r.npm.sankuai.com/react/download/react-17.0.2.tgz"
  integrity sha1-0LXMUW0p6z7uOD91tihkz7aAADc=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

react@^18.3.1:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react/download/react-18.3.1.tgz#49ab892009c53933625bd16b2533fc754cab2891"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/reactcss/download/reactcss-1.2.3.tgz"
  integrity sha1-wAATh15Vexzw39mjaKHD2rO1SN0=
  dependencies:
    lodash "^4.0.1"

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@^4.0.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-4.1.1.tgz"
  integrity sha1-vRFTJxKWctxH+HQI8F35vZyj71U=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/rechoir/download/rechoir-0.6.2.tgz"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redux@^4.0.0, redux@^4.0.4, redux@^4.2.0:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/redux/download/redux-4.2.1.tgz"
  integrity sha1-wI9DBoJsSbXp3JAd7gRS6o/OYZc=
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz"
  integrity sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz"
  integrity sha1-NWreECY/aF3aElEAzYYsHbiVMn8=

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "http://r.npm.sankuai.com/regenerator-transform/download/regenerator-transform-0.15.2.tgz"
  integrity sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.3:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-6.2.0.tgz"
  integrity sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.8.0.tgz"
  integrity sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.12.0.tgz"
  integrity sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=
  dependencies:
    jsesc "~3.0.2"

rehype-raw@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/rehype-raw/download/rehype-raw-7.0.0.tgz"
  integrity sha1-Wdc0j9Xb7zgHu6odRD79LdhezuQ=
  dependencies:
    "@types/hast" "^3.0.0"
    hast-util-raw "^9.0.0"
    vfile "^6.0.0"

remark-gfm@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/remark-gfm/download/remark-gfm-4.0.1.tgz"
  integrity sha1-MyJ7KnQ5dnDTV78FwJjq+FE/DWs=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-gfm "^3.0.0"
    micromark-extension-gfm "^3.0.0"
    remark-parse "^11.0.0"
    remark-stringify "^11.0.0"
    unified "^11.0.0"

remark-parse@^11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/remark-parse/download/remark-parse-11.0.0.tgz"
  integrity sha1-qmB0P8s36/awaSBOtNowTkDbRaE=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    micromark-util-types "^2.0.0"
    unified "^11.0.0"

remark-rehype@^11.0.0:
  version "11.1.1"
  resolved "http://r.npm.sankuai.com/remark-rehype/download/remark-rehype-11.1.1.tgz"
  integrity sha1-+GTdKUeImhGZfAomZ81rOPaFvKc=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    mdast-util-to-hast "^13.0.0"
    unified "^11.0.0"
    vfile "^6.0.0"

remark-stringify@^11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/remark-stringify/download/remark-stringify-11.0.0.tgz"
  integrity sha1-TFsB3XEcJp3xqq4RdD634udjb9M=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-to-markdown "^2.0.0"
    unified "^11.0.0"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request@^2.88.2:
  version "2.88.2"
  resolved "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reqwest@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/reqwest/download/reqwest-2.0.5.tgz"
  integrity sha1-APsVrEkYxBnKgrQ/JMeIguZgOaE=

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-pathname/download/resolve-pathname-3.0.0.tgz"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve@^1.1.6, resolve@^1.14.2, resolve@~1.22.6:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.5.tgz"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-4.0.0.tgz"
  integrity sha1-UZVgpDGJdQlt725gnUQQDtqkzLk=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

right-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/right-align/download/right-align-0.1.3.tgz"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/rollup-plugin-terser/download/rollup-plugin-terser-7.0.2.tgz"
  integrity sha1-6Pu6SGmYGy3DWufopQLVxsBNMk0=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@4.30.0, rollup@^0.25.8, rollup@^4.20.0:
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.30.0.tgz#44ae4260029a8362113ef2a0cee7e02f3f740274"
  integrity sha1-RK5CYAKag2IRPvKgzufgLz90AnQ=
  dependencies:
    "@types/estree" "1.0.6"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.30.0"
    "@rollup/rollup-android-arm64" "4.30.0"
    "@rollup/rollup-darwin-arm64" "4.30.0"
    "@rollup/rollup-darwin-x64" "4.30.0"
    "@rollup/rollup-freebsd-arm64" "4.30.0"
    "@rollup/rollup-freebsd-x64" "4.30.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.30.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.30.0"
    "@rollup/rollup-linux-arm64-gnu" "4.30.0"
    "@rollup/rollup-linux-arm64-musl" "4.30.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.30.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.30.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.30.0"
    "@rollup/rollup-linux-s390x-gnu" "4.30.0"
    "@rollup/rollup-linux-x64-gnu" "4.30.0"
    "@rollup/rollup-linux-x64-musl" "4.30.0"
    "@rollup/rollup-win32-arm64-msvc" "4.30.0"
    "@rollup/rollup-win32-ia32-msvc" "4.30.0"
    "@rollup/rollup-win32-x64-msvc" "4.30.0"
    fsevents "~2.3.2"

rrweb-cssom@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.6.0.tgz"
  integrity sha1-7SmAVbl8vdzesnj5BIV2Kd7F4OE=

rrweb-cssom@^0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.7.1.tgz"
  integrity sha1-xzRRpIS4bdfPseCyiY30twMYPks=

run-async@^2.4.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rw@^1.3.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/rw/download/rw-1.3.3.tgz"
  integrity sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=

rxjs@^7.5.5:
  version "7.8.1"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.1.tgz"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass@^1.63.6:
  version "1.83.3"
  resolved "http://r.npm.sankuai.com/sass/download/sass-1.83.3.tgz"
  integrity sha1-Jqy9DycqE7gnwwF+n4M+8Oqia30=
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

saxes@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/saxes/download/saxes-6.0.0.tgz"
  integrity sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

screenfull@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/screenfull/download/screenfull-5.2.0.tgz"
  integrity sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo=

scroll-into-view-if-needed@^2.2.28, scroll-into-view-if-needed@^2.2.31:
  version "2.2.31"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-2.2.31.tgz"
  integrity sha1-08SClZ3Eg+N5YtFSElTjKV0NFYc=
  dependencies:
    compute-scroll-into-view "^1.0.20"

scroll-into-view-if-needed@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-3.1.0.tgz"
  integrity sha1-+pUkUYx5m0Wi72u/+5K8rQKW0B8=
  dependencies:
    compute-scroll-into-view "^3.0.2"

semver@^5.6.0:
  version "5.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.7, semver@^7.5.3:
  version "7.6.3"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.6.3.tgz"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

shallowequal@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/shallowequal/download/shallowequal-1.1.0.tgz"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@0.8.4:
  version "0.8.4"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.4.tgz"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shelljs@0.8.5:
  version "0.8.5"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.5.tgz"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6, side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

siginfo@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/siginfo/download/siginfo-2.0.0.tgz"
  integrity sha1-MudscLeXJOO7Vny51UPrhYzPrzA=

signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

size-sensor@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/size-sensor/download/size-sensor-1.0.2.tgz"
  integrity sha1-uPjaApaDzytOIvEr+LjwoRRehHE=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slate-history@^0.66.0:
  version "0.66.0"
  resolved "http://r.npm.sankuai.com/slate-history/download/slate-history-0.66.0.tgz"
  integrity sha1-rGP925AwmM60yURDPj91/mOs+UA=
  dependencies:
    is-plain-object "^5.0.0"

slate@^0.72.0:
  version "0.72.8"
  resolved "http://r.npm.sankuai.com/slate/download/slate-0.72.8.tgz"
  integrity sha1-WgGO3yTkVEhlUpOmi/vPVjqluoE=
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-5.0.0.tgz"
  integrity sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

snabbdom@^3.1.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/snabbdom/download/snabbdom-3.6.2.tgz"
  integrity sha1-V91mh49jIEl/p/Z5Qd81agRcdaE=

snake-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/snake-case/download/snake-case-3.0.4.tgz"
  integrity sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@~0.5.1:
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

space-separated-tokens@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/space-separated-tokens/download/space-separated-tokens-2.0.2.tgz"
  integrity sha1-Hs2dI1CjhEVyw/SjErzrAYNIhZ8=

sshpk@^1.7.0:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz"
  integrity sha1-FmPlXN301oi4aka3fw1f42OroCg=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/ssr-window/download/ssr-window-3.0.0.tgz"
  integrity sha1-/VuCgBY4lD4MxwTEaRgBQ1r3rDc=

stackback@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/stackback/download/stackback-0.0.2.tgz"
  integrity sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=

std-env@^3.5.0:
  version "3.8.0"
  resolved "http://r.npm.sankuai.com/std-env/download/std-env-3.8.0.tgz"
  integrity sha1-tW/8G68aKdzICjvfEdf8p8MV59U=

string-argv@0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/string-argv/download/string-argv-0.3.2.tgz"
  integrity sha1-K20O8ktlYnTZV9VOCku/YVPcArY=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0, string-width@^5.0.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10, string.prototype.trim@~1.2.8:
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-entities@^4.0.0:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/stringify-entities/download/stringify-entities-4.0.4.tgz"
  integrity sha1-s7ee9fJ3zErHPK6wI2xbqTmzpPM=
  dependencies:
    character-entities-html4 "^2.0.0"
    character-entities-legacy "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-3.0.0.tgz"
  integrity sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-literal@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/strip-literal/download/strip-literal-2.1.1.tgz"
  integrity sha1-JpBuZfYG1J90hFSggITpQZDC5a0=
  dependencies:
    js-tokens "^9.0.1"

strnum@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/strnum/download/strnum-1.0.5.tgz"
  integrity sha1-XE6Cn+Fa1P8NIMPbWsl7c8mwcts=

style-to-js@^1.0.0:
  version "1.1.16"
  resolved "http://r.npm.sankuai.com/style-to-js/download/style-to-js-1.1.16.tgz"
  integrity sha1-5r1s0p4lC8+PpeZZHQfO11ddvno=
  dependencies:
    style-to-object "1.0.8"

style-to-object@1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/style-to-object/download/style-to-object-1.0.8.tgz"
  integrity sha1-Z6KbykfqpYfbGBGNaPnZWVXoEpI=
  dependencies:
    inline-style-parser "0.2.4"

stylis@^4.3.4:
  version "4.3.5"
  resolved "http://r.npm.sankuai.com/stylis/download/stylis-4.3.5.tgz"
  integrity sha1-QyzJnIHijXBiyI2XnSFjiR6GBIk=

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-parser@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/svg-parser/download/svg-parser-2.0.4.tgz"
  integrity sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=

swr@^2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/swr/download/swr-2.3.3.tgz"
  integrity sha1-nWpwM1XxX5CZ9FEU2z73V2RER4g=
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

tape@^4.5.1:
  version "4.17.0"
  resolved "http://r.npm.sankuai.com/tape/download/tape-4.17.0.tgz"
  integrity sha1-3onzZx3cXa0XjQTCjcawGD9CJo4=
  dependencies:
    "@ljharb/resumer" "~0.0.1"
    "@ljharb/through" "~2.3.9"
    call-bind "~1.0.2"
    deep-equal "~1.1.1"
    defined "~1.0.1"
    dotignore "~0.1.2"
    for-each "~0.3.3"
    glob "~7.2.3"
    has "~1.0.3"
    inherits "~2.0.4"
    is-regex "~1.1.4"
    minimist "~1.2.8"
    mock-property "~1.0.0"
    object-inspect "~1.12.3"
    resolve "~1.22.6"
    string.prototype.trim "~1.2.8"

terser@^5.0.0:
  version "5.37.0"
  resolved "http://r.npm.sankuai.com/terser/download/terser-5.37.0.tgz"
  integrity sha1-OKpm0c/EPQY4+rVOQ/+KT3KiG6M=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/test-exclude/download/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-2.3.0.tgz"
  integrity sha1-/TGGXmZQIHHkEYF+JBRls+nDcuI=

throttle-debounce@^5.0.0, throttle-debounce@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-5.0.2.tgz"
  integrity sha1-7FVJ2E4FPwQ8n9Dypt2JL/hEVrE=

through@^2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tiny-invariant@^1.0.2, tiny-invariant@^1.0.6:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/tiny-invariant/download/tiny-invariant-1.3.3.tgz"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tiny-warning@^1.0.0, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/tiny-warning/download/tiny-warning-1.0.3.tgz"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tinybench@^2.5.1:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/tinybench/download/tinybench-2.9.0.tgz"
  integrity sha1-EDyfi6bXI3pHq23R3P93JRhjQms=

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz"
  integrity sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=

tinypool@^0.8.3:
  version "0.8.4"
  resolved "http://r.npm.sankuai.com/tinypool/download/tinypool-0.8.4.tgz"
  integrity sha1-4hf+EnDZQbOemMYl3OzrsUCMmqg=

tinyspy@^2.2.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/tinyspy/download/tinyspy-2.2.1.tgz"
  integrity sha1-EXsjQvHzig29zHOlCkVIg634YdE=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.3, toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/toggle-selection/download/toggle-selection-1.0.6.tgz"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

tough-cookie@^4.1.4:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.4.tgz"
  integrity sha1-lF8UYbRbWox2ghwz6knDrBksGzY=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-5.0.0.tgz"
  integrity sha1-O0bVg2E+xygwINeQGfEzVyOAHOw=
  dependencies:
    punycode "^2.3.1"

trim-lines@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/trim-lines/download/trim-lines-3.0.1.tgz"
  integrity sha1-2ALjMqB9+GHEiALAQyEBexvYczg=

trough@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/trough/download/trough-2.2.0.tgz"
  integrity sha1-lKYL1r03XBUsHfkRpLEdWwJW9Q8=

ts-polyfill@^3.0.1:
  version "3.8.2"
  resolved "http://r.npm.sankuai.com/ts-polyfill/download/ts-polyfill-3.8.2.tgz"
  integrity sha1-c3XGtqSuB0r09iAP9sDTLbvRE8s=
  dependencies:
    core-js "^3.6.4"

tsconfck@^3.0.3:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/tsconfck/download/tsconfck-3.1.4.tgz"
  integrity sha1-3gGhUzSWLi/rUmgkM5tRviZxIik=

tslib@2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.3.0.tgz"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tslib@^1.10.0, tslib@^1.8.1:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.1, tslib@^2.4.1:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@^4.0.0, type-detect@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/type-detect/download/type-detect-4.1.0.tgz"
  integrity sha1-3rJFPo8I3K566YxiaxPd2wFVkGw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^1.0.2:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-1.4.0.tgz"
  integrity sha1-6fuBP+O/F0TsNZ1V0a/++nbxS+E=

type@^2.7.2:
  version "2.7.3"
  resolved "http://r.npm.sankuai.com/type/download/type-2.7.3.tgz"
  integrity sha1-Q2mBZSEpKFzDupTzkohsJjfqBIY=

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@^4.9.3:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

ufo@^1.5.4:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/ufo/download/ufo-1.5.4.tgz"
  integrity sha1-FtaUlnTKDJ4Pu64fogpx17He11Q=

uglify-js@^2.6.2:
  version "2.8.29"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-2.8.29.tgz"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.19.2:
  version "6.19.8"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.19.8.tgz"
  integrity sha1-NREcnRQ3q4OnzcCrri8m2I7aCgI=

undici-types@~6.20.0:
  version "6.20.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.20.0.tgz"
  integrity sha1-gXG/IsH1iNFVTVW/IEvGJK84hDM=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha1-yzFz/kfKdD4ighbko93EyE1ijMI=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

unified@^11.0.0:
  version "11.0.5"
  resolved "http://r.npm.sankuai.com/unified/download/unified-11.0.5.tgz"
  integrity sha1-9mZ3YQpcCp7pDKsrjU1mA3Am2eE=
  dependencies:
    "@types/unist" "^3.0.0"
    bail "^2.0.0"
    devlop "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^4.0.0"
    trough "^2.0.0"
    vfile "^6.0.0"

unist-util-is@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-is/download/unist-util-is-6.0.0.tgz"
  integrity sha1-t3WVZIav8Qep3tlx2ZbBczdL5CQ=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-position@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-position/download/unist-util-position-5.0.0.tgz"
  integrity sha1-Z48gq1yhIHqX1+qKOINzyc+Ja+Q=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-stringify-position/download/unist-util-stringify-position-4.0.0.tgz"
  integrity sha1-RJxuIaiA4IVb9aq63rOnQDFKusI=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/unist-util-visit-parents/download/unist-util-visit-parents-6.0.1.tgz"
  integrity sha1-TV+FdVw7jw3GniHspdbYLSIWKBU=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-visit/download/unist-util-visit-5.0.0.tgz"
  integrity sha1-p94fMfcv/TUZ6nGBTMz1/WqSF9Y=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

update-browserslist-db@^1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.2.tgz"
  integrity sha1-l+nJarCue8rAjprlFR0m5rxrVYA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-memo-one@^1.1.1:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/use-memo-one/download/use-memo-one-1.1.3.tgz"
  integrity sha1-L9LkOiFp6rx0lpYKzox57++XXpk=

use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

uuid@^10.0.0:
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-10.0.0.tgz#5a95aa454e6e002725c79055fd42aaba30ca6294"
  integrity sha1-WpWqRU5uACclx5BV/UKqujDKYpQ=

uuid@^3.3.2:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-9.0.1.tgz"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

value-equal@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/value-equal/download/value-equal-1.0.1.tgz"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-location@^5.0.0:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/vfile-location/download/vfile-location-5.0.3.tgz"
  integrity sha1-y56s0g8rZCbRlFHg6vo9CoRiJcM=
  dependencies:
    "@types/unist" "^3.0.0"
    vfile "^6.0.0"

vfile-message@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/vfile-message/download/vfile-message-4.0.2.tgz"
  integrity sha1-yIPJ9nfHLBZjYv1jXyH8Flp9EYE=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"

vfile@^6.0.0:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/vfile/download/vfile-6.0.3.tgz"
  integrity sha1-NlKrHEllMYUr9VprrFevmB68OKs=
  dependencies:
    "@types/unist" "^3.0.0"
    vfile-message "^4.0.0"

vite-node@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/vite-node/download/vite-node-1.6.0.tgz"
  integrity sha1-LH5hEpv+zHWUePpZJ1T9lwSqun8=
  dependencies:
    cac "^6.7.14"
    debug "^4.3.4"
    pathe "^1.1.1"
    picocolors "^1.0.0"
    vite "^5.0.0"

vite-plugin-html-template@^1.2.0:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/vite-plugin-html-template/download/vite-plugin-html-template-1.2.2.tgz"
  integrity sha1-0mPBjc9fXlS8dIlFRv0O2ZMZHy8=
  dependencies:
    shelljs "0.8.4"

vite-plugin-mpa@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-mpa/download/vite-plugin-mpa-1.2.0.tgz"
  integrity sha1-Zf3ksTYz4QGty4jXM2adk7zqb78=
  dependencies:
    connect-history-api-fallback "1.6.0"
    shelljs "0.8.5"
    yargs "16.2.0"

vite-plugin-qiankun@^1.0.15:
  version "1.0.15"
  resolved "http://r.npm.sankuai.com/vite-plugin-qiankun/download/vite-plugin-qiankun-1.0.15.tgz"
  integrity sha1-hiu2k1xQ2zFTbPMi4T879Z4a2s4=
  dependencies:
    cheerio "^1.0.0-rc.10"

vite-plugin-svgr@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-svgr/download/vite-plugin-svgr-4.3.0.tgz"
  integrity sha1-dC8W8RN1mWMGxpbsMj5NI/YAUHU=
  dependencies:
    "@rollup/pluginutils" "^5.1.3"
    "@svgr/core" "^8.1.0"
    "@svgr/plugin-jsx" "^8.1.0"

vite-plugin-top-level-await@^1.3.1:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-top-level-await/download/vite-plugin-top-level-await-1.5.0.tgz#e3f76302921152bf29d1658f169d168f8937e78b"
  integrity sha1-4/djApIRUr8p0WWPFp0Wj4k354s=
  dependencies:
    "@rollup/plugin-virtual" "^3.0.2"
    "@swc/core" "^1.10.16"
    uuid "^10.0.0"

vite-tsconfig-paths@^4.0.5:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/vite-tsconfig-paths/download/vite-tsconfig-paths-4.3.2.tgz"
  integrity sha1-Mh8C5Lc2qQ/2L5CGRn+vTi2oV6k=
  dependencies:
    debug "^4.1.1"
    globrex "^0.1.2"
    tsconfck "^3.0.3"

vite@^5.0.0, vite@^5.1.8:
  version "5.4.11"
  resolved "http://r.npm.sankuai.com/vite/download/vite-5.4.11.tgz"
  integrity sha1-O0Fc1K7XgaNWwd5anrr7g3cV9uU=
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

vitest@^1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/vitest/download/vitest-1.6.0.tgz"
  integrity sha1-nVrUdSo8RRvpGeQSxZcSbP+5iS8=
  dependencies:
    "@vitest/expect" "1.6.0"
    "@vitest/runner" "1.6.0"
    "@vitest/snapshot" "1.6.0"
    "@vitest/spy" "1.6.0"
    "@vitest/utils" "1.6.0"
    acorn-walk "^8.3.2"
    chai "^4.3.10"
    debug "^4.3.4"
    execa "^8.0.1"
    local-pkg "^0.5.0"
    magic-string "^0.30.5"
    pathe "^1.1.1"
    picocolors "^1.0.0"
    std-env "^3.5.0"
    strip-literal "^2.0.0"
    tinybench "^2.5.1"
    tinypool "^0.8.3"
    vite "^5.0.0"
    vite-node "1.6.0"
    why-is-node-running "^2.2.2"

w3c-xmlserializer@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-5.0.0.tgz"
  integrity sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=
  dependencies:
    xml-name-validator "^5.0.0"

warning@^4.0.1, warning@^4.0.2, warning@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/warning/download/warning-4.0.3.tgz"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

web-namespaces@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/web-namespaces/download/web-namespaces-2.0.1.tgz"
  integrity sha1-EBD/fGUOzLJZLOvur5obJT/UBpI=

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

whatwg-encoding@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-3.1.1.tgz"
  integrity sha1-0PTvdpkF1CbhaI8+NDgambYLduU=
  dependencies:
    iconv-lite "0.6.3"

whatwg-fetch@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-2.0.4.tgz"
  integrity sha1-3eal3zFfnTmZGqF2IYU9cguFVm8=

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-4.0.0.tgz"
  integrity sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=

whatwg-url@^14.0.0:
  version "14.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-14.0.0.tgz"
  integrity sha1-ALqqf9GYdEkQxLHvaDePIgDkzrY=
  dependencies:
    tr46 "^5.0.0"
    webidl-conversions "^7.0.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.18"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.18.tgz"
  integrity sha1-3yOJ6/P7skanE5DpBzCp7bbOF60=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

why-is-node-running@^2.2.2:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/why-is-node-running/download/why-is-node-running-2.3.0.tgz"
  integrity sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=
  dependencies:
    siginfo "^2.0.0"
    stackback "0.0.2"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/wildcard/download/wildcard-1.1.2.tgz"
  integrity sha1-pwIEUwhNjNLv5wup02liY94XEKU=

window-size@0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/window-size/download/window-size-0.1.0.tgz"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wordwrap@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/wordwrap/download/wordwrap-0.0.2.tgz"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

wrap-ansi@^6.0.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.0.1, wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^8.18.0:
  version "8.18.0"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.18.0.tgz"
  integrity sha1-DXUFpur+Kw5xLSMrQiefU7wom7w=

xml-name-validator@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-5.0.0.tgz"
  integrity sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-2.3.1.tgz"
  integrity sha1-Av4JddI81EEkKqcgTgn8KKwqwzs=

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@16.2.0:
  version "16.2.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@~3.10.0:
  version "3.10.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-3.10.0.tgz"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yocto-queue@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-1.1.1.tgz"
  integrity sha1-/vZc46yfijLOrFpjT3ThflsjIRA=

zrender@6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/zrender/download/zrender-6.0.0.tgz"
  integrity sha1-lHB3vGnN6nRBNJhJJ/Ey83J/gHk=
  dependencies:
    tslib "2.3.0"

zustand@^5.0.1:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/zustand/download/zustand-5.0.3.tgz"
  integrity sha1-syNDW3PQayUS6Tx3I5Y0N0sOQH8=

zwitch@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/zwitch/download/zwitch-2.0.4.tgz"
  integrity sha1-yCfUsKy3b8PmhaTG7CkC1RBw6dc=
